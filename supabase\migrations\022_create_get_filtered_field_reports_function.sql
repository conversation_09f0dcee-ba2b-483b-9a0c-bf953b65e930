-- Create the missing get_filtered_field_reports function
-- This fixes the 404 error when fetching field reports

CREATE OR REPLACE FUNCTION get_filtered_field_reports(
    p_staff_id UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL,
    p_date_from DATE DEFAULT NULL,
    p_date_to DATE DEFAULT NULL,
    p_limit INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    school_id UUID,
    report_type TEXT,
    title TEXT,
    description TEXT,
    findings TEXT,
    recommendations TEXT,
    photos TEXT[],
    gps_coordinates POINT,
    reported_by UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    attendance_id UUID,
    staff_id UUID,
    report_date DATE,
    activity_type field_activity_type,
    round_table_sessions INTEGER,
    total_students INTEGER,
    students_per_session INTEGER,
    activities_conducted TEXT[],
    topics_covered TEXT[],
    challenges TEXT,
    wins TEXT,
    observations TEXT,
    lessons_learned TEXT,
    follow_up_required BOOLEAN,
    follow_up_actions TEXT,
    notes TEXT,
    offline_sync BOOLEAN,
    facilitators JSONB,
    venue_location TEXT,
    activity_dates TEXT,
    male_participants INTEGER,
    female_participants INTEGER,
    students_primary INTEGER,
    students_s1 INTEGER,
    students_s2 INTEGER,
    students_s3 INTEGER,
    students_s4 INTEGER,
    students_other INTEGER,
    champions_count INTEGER,
    round_tables_primary INTEGER,
    round_tables_s1 INTEGER,
    round_tables_s2 INTEGER,
    round_tables_s3 INTEGER,
    round_tables_s4 INTEGER,
    round_tables_other INTEGER,
    activity_feedback JSONB,
    introduction TEXT,
    way_forward TEXT,
    -- Additional fields for enhanced view compatibility
    staff_name TEXT,
    school_name TEXT,
    check_in_time TIMESTAMPTZ,
    check_out_time TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role TEXT;
BEGIN
    -- Get current user info
    current_user_id := auth.uid();
    
    SELECT role INTO current_user_role 
    FROM profiles 
    WHERE id = current_user_id;
    
    -- Check permissions: users can only see their own data unless admin/program_officer
    IF p_staff_id IS NOT NULL AND p_staff_id != current_user_id AND current_user_role NOT IN ('admin', 'program_officer') THEN
        RAISE EXCEPTION 'Insufficient permissions to view field reports for other users';
    END IF;

    RETURN QUERY
    SELECT 
        fr.id,
        fr.school_id,
        fr.report_type,
        fr.title,
        fr.description,
        fr.findings,
        fr.recommendations,
        fr.photos,
        fr.gps_coordinates,
        fr.reported_by,
        fr.created_at,
        fr.updated_at,
        fr.attendance_id,
        fr.staff_id,
        fr.report_date,
        fr.activity_type,
        fr.round_table_sessions,
        fr.total_students,
        fr.students_per_session,
        fr.activities_conducted,
        fr.topics_covered,
        fr.challenges,
        fr.wins,
        fr.observations,
        fr.lessons_learned,
        fr.follow_up_required,
        fr.follow_up_actions,
        fr.notes,
        fr.offline_sync,
        fr.facilitators,
        fr.venue_location,
        fr.activity_dates,
        fr.male_participants,
        fr.female_participants,
        fr.students_primary,
        fr.students_s1,
        fr.students_s2,
        fr.students_s3,
        fr.students_s4,
        fr.students_other,
        fr.champions_count,
        fr.round_tables_primary,
        fr.round_tables_s1,
        fr.round_tables_s2,
        fr.round_tables_s3,
        fr.round_tables_s4,
        fr.round_tables_other,
        fr.activity_feedback,
        fr.introduction,
        fr.way_forward,
        -- Additional fields
        p.name::TEXT as staff_name,
        s.name::TEXT as school_name,
        fsa.check_in_time,
        fsa.check_out_time
    FROM field_reports fr
    LEFT JOIN profiles p ON fr.reported_by = p.id
    LEFT JOIN schools s ON fr.school_id = s.id
    LEFT JOIN field_staff_attendance fsa ON fr.attendance_id = fsa.id
    WHERE 
        -- Role-based filtering
        (current_user_role IN ('admin', 'program_officer') OR fr.reported_by = current_user_id)
        -- Parameter filtering
        AND (p_staff_id IS NULL OR fr.staff_id = p_staff_id OR fr.reported_by = p_staff_id)
        AND (p_school_id IS NULL OR fr.school_id = p_school_id)
        AND (p_date_from IS NULL OR fr.report_date >= p_date_from)
        AND (p_date_to IS NULL OR fr.report_date <= p_date_to)
    ORDER BY fr.report_date DESC, fr.created_at DESC
    LIMIT COALESCE(p_limit, 100); -- Default limit to prevent large queries
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_filtered_field_reports(UUID, UUID, DATE, DATE, INTEGER) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_filtered_field_reports(UUID, UUID, DATE, DATE, INTEGER) IS 'Get filtered field reports with role-based access control and enhanced data';
