/**
 * Conflict resolution utilities for offline sync
 * Handles conflict detection, resolution strategies, and conflict management
 */

import { 
  SyncConflict, 
  ConflictData, 
  OfflineData,
  CONFLICT_RESOLUTION 
} from '@/types/offlineSync.types';
import { loadConflicts, saveConflicts } from '@/utils/offlineStorage';

/**
 * Detect conflicts between local and server data
 */
export const detectConflicts = (
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>
): string[] => {
  const conflictFields: string[] = [];
  
  // Get all unique keys from both objects
  const allKeys = new Set([...Object.keys(localData), ...Object.keys(serverData)]);
  
  for (const key of allKeys) {
    const localValue = localData[key];
    const serverValue = serverData[key];
    
    // Skip timestamp fields as they're expected to differ
    if (key.includes('timestamp') || key.includes('updated_at') || key.includes('created_at')) {
      continue;
    }
    
    // Deep comparison for objects and arrays
    if (JSON.stringify(localValue) !== JSON.stringify(serverValue)) {
      conflictFields.push(key);
    }
  }
  
  return conflictFields;
};

/**
 * Create a conflict record
 */
export const createConflict = (
  id: string,
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>,
  type: string = 'data_conflict'
): ConflictData => {
  const conflictFields = detectConflicts(localData, serverData);
  
  return {
    id,
    localData,
    serverData,
    conflictFields,
    timestamp: Date.now(),
    type
  };
};

/**
 * Enhanced conflict resolution with safety checks and backup
 */
export const resolveConflict = (
  conflict: ConflictData,
  strategy: keyof typeof CONFLICT_RESOLUTION,
  customResolution?: Record<string, unknown>
): { resolvedData: Record<string, unknown>; backup: ConflictBackup } => {
  // Create backup before resolution
  const backup: ConflictBackup = {
    id: conflict.id,
    originalConflict: { ...conflict },
    strategy,
    timestamp: Date.now(),
    customResolution: customResolution ? { ...customResolution } : undefined,
  };

  let resolvedData: Record<string, unknown>;

  switch (strategy) {
    case 'CLIENT_WINS':
      resolvedData = { ...conflict.localData };
      break;

    case 'SERVER_WINS':
      resolvedData = { ...conflict.serverData };
      break;

    case 'MERGE':
      resolvedData = mergeConflictDataSafely(conflict.localData, conflict.serverData);
      break;

    case 'MANUAL':
      if (!customResolution) {
        throw new Error('Manual resolution requires custom resolution data');
      }
      resolvedData = validateCustomResolution(customResolution, conflict);
      break;

    default:
      throw new Error(`Unknown conflict resolution strategy: ${strategy}`);
  }

  // Store backup for potential undo
  storeConflictBackup(backup);

  return { resolvedData, backup };
};

/**
 * Backup interface for conflict resolution
 */
export interface ConflictBackup {
  id: string;
  originalConflict: ConflictData;
  strategy: keyof typeof CONFLICT_RESOLUTION;
  timestamp: number;
  customResolution?: Record<string, unknown>;
}

/**
 * Validate custom resolution data
 */
const validateCustomResolution = (
  customData: Record<string, unknown>,
  conflict: ConflictData
): Record<string, unknown> => {
  const validated = { ...customData };

  // Ensure all required fields are present
  const allFields = new Set([
    ...Object.keys(conflict.localData),
    ...Object.keys(conflict.serverData)
  ]);

  for (const field of allFields) {
    if (!(field in validated)) {
      // Use local data as fallback if field is missing
      validated[field] = conflict.localData[field] ?? conflict.serverData[field];
    }
  }

  return validated;
};

/**
 * Enhanced merge with safety checks
 */
const mergeConflictDataSafely = (
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>
): Record<string, unknown> => {
  const merged = { ...serverData }; // Start with server data as base

  // Get all unique keys from both objects
  const allKeys = new Set([...Object.keys(localData), ...Object.keys(serverData)]);

  for (const key of allKeys) {
    const localValue = localData[key];
    const serverValue = serverData[key];

    // Skip system fields that should always come from server
    if (key.includes('id') || key.includes('created_at')) {
      merged[key] = serverValue ?? localValue;
      continue;
    }

    // For updated_at, use the most recent
    if (key.includes('updated_at')) {
      const localTime = new Date(localValue as string).getTime();
      const serverTime = new Date(serverValue as string).getTime();
      merged[key] = localTime > serverTime ? localValue : serverValue;
      continue;
    }

    // For arrays, merge unique values
    if (Array.isArray(localValue) && Array.isArray(serverValue)) {
      merged[key] = [...new Set([...serverValue, ...localValue])];
      continue;
    }

    // For objects, recursively merge
    if (
      typeof localValue === 'object' &&
      typeof serverValue === 'object' &&
      localValue !== null &&
      serverValue !== null
    ) {
      merged[key] = mergeConflictDataSafely(
        localValue as Record<string, unknown>,
        serverValue as Record<string, unknown>
      );
      continue;
    }

    // For primitive values, prefer non-empty local values
    if (localValue !== null && localValue !== undefined && localValue !== '') {
      merged[key] = localValue;
    } else {
      merged[key] = serverValue;
    }
  }

  return merged;
};

/**
 * Intelligent merge of conflicting data
 */
export const mergeConflictData = (
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>
): Record<string, unknown> => {
  const merged = { ...serverData }; // Start with server data as base
  
  // Apply local changes that don't conflict with server updates
  for (const [key, localValue] of Object.entries(localData)) {
    const serverValue = serverData[key];
    
    // If server doesn't have this field, use local value
    if (!(key in serverData)) {
      merged[key] = localValue;
      continue;
    }
    
    // For timestamps, use the most recent
    if (key.includes('timestamp') || key.includes('updated_at')) {
      const localTime = typeof localValue === 'number' ? localValue : Date.parse(String(localValue));
      const serverTime = typeof serverValue === 'number' ? serverValue : Date.parse(String(serverValue));
      merged[key] = Math.max(localTime, serverTime);
      continue;
    }
    
    // For arrays, merge unique values
    if (Array.isArray(localValue) && Array.isArray(serverValue)) {
      merged[key] = [...new Set([...serverValue, ...localValue])];
      continue;
    }
    
    // For objects, recursively merge
    if (
      typeof localValue === 'object' && 
      typeof serverValue === 'object' && 
      localValue !== null && 
      serverValue !== null
    ) {
      merged[key] = mergeConflictData(
        localValue as Record<string, unknown>,
        serverValue as Record<string, unknown>
      );
      continue;
    }
    
    // For primitive values, prefer local if it's more recent or non-empty
    if (localValue && (!serverValue || localValue !== serverValue)) {
      merged[key] = localValue;
    }
  }
  
  return merged;
};

/**
 * Add conflict to storage
 */
export const addConflict = (conflict: ConflictData): void => {
  try {
    const existingConflicts = loadConflicts();
    
    // Check if conflict already exists
    const existingIndex = existingConflicts.findIndex(c => c.id === conflict.id);
    
    if (existingIndex >= 0) {
      // Update existing conflict
      existingConflicts[existingIndex] = conflict;
    } else {
      // Add new conflict
      existingConflicts.push(conflict);
    }
    
    saveConflicts(existingConflicts);
  } catch (error) {
    console.error('Failed to add conflict:', error);
    throw new Error('Failed to save conflict data');
  }
};

/**
 * Remove conflict from storage
 */
export const removeConflict = (conflictId: string): boolean => {
  try {
    const existingConflicts = loadConflicts();
    const filteredConflicts = existingConflicts.filter(c => c.id !== conflictId);
    
    if (filteredConflicts.length === existingConflicts.length) {
      return false; // Conflict not found
    }
    
    saveConflicts(filteredConflicts);
    return true;
  } catch (error) {
    console.error('Failed to remove conflict:', error);
    return false;
  }
};

/**
 * Get all conflicts
 */
export const getAllConflicts = (): ConflictData[] => {
  return loadConflicts();
};

/**
 * Get conflicts by type
 */
export const getConflictsByType = (type: string): ConflictData[] => {
  return loadConflicts().filter(conflict => conflict.type === type);
};

/**
 * Get conflict by ID
 */
export const getConflictById = (id: string): ConflictData | null => {
  const conflicts = loadConflicts();
  return conflicts.find(conflict => conflict.id === id) || null;
};

/**
 * Clear all conflicts
 */
export const clearAllConflicts = (): void => {
  saveConflicts([]);
};

/**
 * Store conflict backup for potential undo
 */
const storeConflictBackup = (backup: ConflictBackup): void => {
  try {
    const existingBackups = getConflictBackups();
    existingBackups.push(backup);

    // Keep only last 10 backups to prevent storage bloat
    const recentBackups = existingBackups
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10);

    localStorage.setItem('ilead_conflict_backups', JSON.stringify(recentBackups));
  } catch (error) {
    console.error('Failed to store conflict backup:', error);
  }
};

/**
 * Get all conflict backups
 */
export const getConflictBackups = (): ConflictBackup[] => {
  try {
    const stored = localStorage.getItem('ilead_conflict_backups');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Failed to load conflict backups:', error);
    return [];
  }
};

/**
 * Undo a conflict resolution
 */
export const undoConflictResolution = (backupId: string): boolean => {
  try {
    const backups = getConflictBackups();
    const backup = backups.find(b => b.id === backupId);

    if (!backup) {
      throw new Error('Backup not found');
    }

    // Restore the original conflict
    addConflict(backup.originalConflict);

    // Remove the backup
    const updatedBackups = backups.filter(b => b.id !== backupId);
    localStorage.setItem('ilead_conflict_backups', JSON.stringify(updatedBackups));

    return true;
  } catch (error) {
    console.error('Failed to undo conflict resolution:', error);
    return false;
  }
};

/**
 * Clear old conflict backups
 */
export const cleanupConflictBackups = (maxAgeMs: number = 7 * 24 * 60 * 60 * 1000): void => {
  try {
    const backups = getConflictBackups();
    const cutoffTime = Date.now() - maxAgeMs;

    const recentBackups = backups.filter(backup => backup.timestamp > cutoffTime);
    localStorage.setItem('ilead_conflict_backups', JSON.stringify(recentBackups));
  } catch (error) {
    console.error('Failed to cleanup conflict backups:', error);
  }
};

/**
 * Get conflict resolution recommendations
 */
export const getConflictRecommendations = (conflict: ConflictData): {
  recommended: keyof typeof CONFLICT_RESOLUTION;
  reasons: string[];
  warnings: string[];
} => {
  const reasons: string[] = [];
  const warnings: string[] = [];
  let recommended: keyof typeof CONFLICT_RESOLUTION = 'MERGE';

  // Analyze conflict characteristics
  const hasLocationData = conflict.conflictFields.some(field =>
    field.includes('latitude') || field.includes('longitude') || field.includes('accuracy')
  );

  const hasTimestamps = conflict.conflictFields.some(field =>
    field.includes('timestamp') || field.includes('updated_at')
  );

  const hasUserInput = conflict.conflictFields.some(field =>
    field.includes('notes') || field.includes('participants') || field.includes('activity')
  );

  // Determine recommendation based on data types
  if (hasLocationData && hasUserInput) {
    recommended = 'MERGE';
    reasons.push('Location data can be merged with user input safely');
    reasons.push('Preserves both GPS accuracy and manual entries');
  } else if (hasUserInput && !hasLocationData) {
    recommended = 'CLIENT_WINS';
    reasons.push('User input is typically more accurate than automated data');
    reasons.push('Local changes contain manual corrections');
  } else if (hasLocationData && !hasUserInput) {
    recommended = 'SERVER_WINS';
    reasons.push('Server location data may be more recent');
    warnings.push('Local GPS readings will be lost');
  } else {
    recommended = 'MERGE';
    reasons.push('Smart merge can combine both versions safely');
  }

  // Add general warnings
  if (conflict.conflictFields.length > 5) {
    warnings.push('Large number of conflicting fields - review carefully');
  }

  const conflictAge = Date.now() - conflict.timestamp;
  if (conflictAge > 24 * 60 * 60 * 1000) { // Older than 1 day
    warnings.push('Old conflict - data may be outdated');
  }

  return { recommended, reasons, warnings };
};

/**
 * Auto-resolve conflicts based on predefined rules
 */
export const autoResolveConflicts = (
  strategy: keyof typeof CONFLICT_RESOLUTION = 'MERGE'
): { resolved: number; failed: number; backups: ConflictBackup[] } => {
  const conflicts = loadConflicts();
  let resolved = 0;
  let failed = 0;
  const backups: ConflictBackup[] = [];

  const remainingConflicts: ConflictData[] = [];

  for (const conflict of conflicts) {
    try {
      // Only auto-resolve if strategy is not MANUAL
      if (strategy !== 'MANUAL') {
        const result = resolveConflict(conflict, strategy);
        backups.push(result.backup);
        resolved++;
      } else {
        remainingConflicts.push(conflict);
      }
    } catch (error) {
      console.error(`Failed to auto-resolve conflict ${conflict.id}:`, error);
      remainingConflicts.push(conflict);
      failed++;
    }
  }

  // Save remaining conflicts
  saveConflicts(remainingConflicts);

  return { resolved, failed, backups };
};

/**
 * Validate conflict resolution data
 */
export const validateResolution = (
  conflict: ConflictData,
  resolution: Record<string, unknown>
): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check that all required fields from original data are present
  const requiredFields = new Set([
    ...Object.keys(conflict.localData),
    ...Object.keys(conflict.serverData)
  ]);
  
  for (const field of requiredFields) {
    if (!(field in resolution)) {
      errors.push(`Missing required field: ${field}`);
    }
  }
  
  // Validate data types match original data
  for (const [key, value] of Object.entries(resolution)) {
    const localType = typeof conflict.localData[key];
    const serverType = typeof conflict.serverData[key];
    const resolutionType = typeof value;
    
    if (localType !== 'undefined' && resolutionType !== localType) {
      errors.push(`Type mismatch for field ${key}: expected ${localType}, got ${resolutionType}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Get conflict statistics
 */
export const getConflictStats = (): {
  total: number;
  byType: Record<string, number>;
  oldestConflict: number | null;
  newestConflict: number | null;
} => {
  const conflicts = loadConflicts();
  
  const byType: Record<string, number> = {};
  const timestamps = conflicts.map(c => c.timestamp);
  
  for (const conflict of conflicts) {
    byType[conflict.type] = (byType[conflict.type] || 0) + 1;
  }
  
  return {
    total: conflicts.length,
    byType,
    oldestConflict: timestamps.length > 0 ? Math.min(...timestamps) : null,
    newestConflict: timestamps.length > 0 ? Math.max(...timestamps) : null
  };
};
