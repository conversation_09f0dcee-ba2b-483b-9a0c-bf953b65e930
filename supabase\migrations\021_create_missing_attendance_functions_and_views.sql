-- Create missing attendance functions and field_visits view
-- This fixes the 404 errors in the field staff dashboard

-- 1. Create the missing get_filtered_field_staff_attendance function
CREATE OR REPLACE FUNCTION get_filtered_field_staff_attendance(
    attendance_date DATE DEFAULT CURRENT_DATE,
    status TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    staff_id UUID,
    staff_name TEXT,
    school_id UUID,
    school_name TEXT,
    attendance_date DATE,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    total_duration_minutes INTEGER,
    status TEXT,
    location_verified BOOLEAN,
    distance_from_school NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fsa.id,
        fsa.staff_id,
        p.name::TEXT as staff_name,
        fsa.school_id,
        s.name::TEXT as school_name,
        fsa.attendance_date,
        fsa.check_in_time,
        fsa.check_out_time,
        fsa.total_duration_minutes,
        COALESCE(fsa.status, 'active')::TEXT as status,
        fsa.location_verified,
        fsa.distance_from_school
    FROM field_staff_attendance fsa
    JOIN profiles p ON fsa.staff_id = p.id
    JOIN schools s ON fsa.school_id = s.id
    WHERE 
        (attendance_date IS NULL OR fsa.attendance_date = get_filtered_field_staff_attendance.attendance_date)
        AND (status IS NULL OR COALESCE(fsa.status, 'active') = get_filtered_field_staff_attendance.status)
    ORDER BY fsa.check_in_time DESC;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_filtered_field_staff_attendance(DATE, TEXT) TO authenticated;

-- 2. Create field_visits view that maps to existing attendance data
CREATE OR REPLACE VIEW field_visits AS
SELECT 
    fsa.id,
    fsa.staff_id,
    fsa.school_id,
    fsa.attendance_date as visit_date,
    fsa.check_in_time as created_at,
    fsa.total_duration_minutes as duration_minutes,
    fsa.check_in_location as location,
    fsa.check_in_address as address,
    fsa.status,
    fsa.notes,
    -- Map field reports if they exist for this attendance session
    fr.id as report_id,
    fr.title as report_title,
    fr.description as report_description
FROM field_staff_attendance fsa
LEFT JOIN field_reports fr ON fr.school_id = fsa.school_id 
    AND DATE(fr.created_at) = fsa.attendance_date
    AND fr.reported_by = fsa.staff_id;

-- Grant select permissions on the view
GRANT SELECT ON field_visits TO authenticated;

-- 3. Create RLS policy for field_visits view
CREATE POLICY "Users can view their own field visits" ON field_staff_attendance
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- 4. Create a function to get field visit statistics for dashboard
CREATE OR REPLACE FUNCTION get_field_visit_stats(
    p_staff_id UUID,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    total_visits BIGINT,
    this_week_visits BIGINT,
    average_duration_minutes NUMERIC,
    total_duration_minutes BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check permissions
    IF p_staff_id != auth.uid() AND NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to view field visit stats';
    END IF;

    RETURN QUERY
    SELECT 
        COUNT(*) as total_visits,
        COUNT(*) FILTER (
            WHERE fsa.attendance_date >= CURRENT_DATE - INTERVAL '7 days'
        ) as this_week_visits,
        ROUND(AVG(fsa.total_duration_minutes), 2) as average_duration_minutes,
        COALESCE(SUM(fsa.total_duration_minutes), 0) as total_duration_minutes
    FROM field_staff_attendance fsa
    WHERE 
        fsa.staff_id = p_staff_id
        AND fsa.attendance_date >= CURRENT_DATE - INTERVAL '1 day' * p_days
        AND fsa.check_out_time IS NOT NULL; -- Only completed visits
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_field_visit_stats(UUID, INTEGER) TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION get_filtered_field_staff_attendance(DATE, TEXT) IS 'Get filtered field staff attendance records with staff and school details';
COMMENT ON VIEW field_visits IS 'View that maps field staff attendance to field visits for compatibility';
COMMENT ON FUNCTION get_field_visit_stats(UUID, INTEGER) IS 'Get field visit statistics for a specific staff member';
