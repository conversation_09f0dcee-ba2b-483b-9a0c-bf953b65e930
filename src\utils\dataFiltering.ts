import { supabase } from '@/integrations/supabase/client';
import { UserRole } from '@/utils/rbac';
import { useAuth } from '@/hooks/useAuth';

export interface DataFilterConfig {
  userRole: UserRole;
  userId: string;
  targetUserId?: string;
  allowSelfAccess?: boolean;
  requireOwnership?: boolean;
}

export interface FilteredQueryBuilder {
  query: any;
  hasAccess: boolean;
  reason?: string;
}

/**
 * Apply role-based filtering to Supabase queries
 */
export class DataFilterService {
  /**
   * Filter field reports based on user role using RPC function
   */
  static filterFieldReports(config: DataFilterConfig, options?: {
    schoolId?: string;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
  }): FilteredQueryBuilder {
    const { userRole, userId, targetUserId } = config;

    // Use the server-side filtering RPC function
    const query = supabase.rpc('get_filtered_field_reports', {
      p_staff_id: targetUserId || (userRole === 'field_staff' ? userId : null),
      p_school_id: options?.schoolId || null,
      p_date_from: options?.dateFrom || null,
      p_date_to: options?.dateTo || null,
      p_limit: options?.limit || null,
    });

    return {
      query,
      hasAccess: true,
      reason: 'Using server-side filtering for field reports'
    };
  }

  /**
   * Filter field staff attendance based on user role using RPC function
   */
  static filterFieldStaffAttendance(config: DataFilterConfig, options?: {
    dateFrom?: string;
    dateTo?: string;
  }): FilteredQueryBuilder {
    const { userRole, userId, targetUserId } = config;

    // Use the server-side filtering RPC function
    const query = supabase.rpc('get_filtered_field_staff_attendance', {
      p_staff_id: targetUserId || (userRole === 'field_staff' ? userId : null),
      p_date_from: options?.dateFrom || null,
      p_date_to: options?.dateTo || null,
    });

    return {
      query,
      hasAccess: true,
      reason: 'Using server-side filtering for field staff attendance'
    };
  }

  /**
   * Filter tasks based on user role
   */
  static filterTasks(config: DataFilterConfig): FilteredQueryBuilder {
    const { userRole, userId } = config;
    
    let query = supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: false });

    // Admin can see all tasks
    if (userRole === 'admin') {
      return { query, hasAccess: true };
    }

    // Program officers and field staff see tasks they created or are assigned to
    if (userRole === 'program_officer' || userRole === 'field_staff') {
      query = query.or(`assigned_to.eq.${userId},created_by.eq.${userId}`);
      return { 
        query, 
        hasAccess: true,
        reason: 'Users can only access tasks they created or are assigned to'
      };
    }

    return { 
      query, 
      hasAccess: false, 
      reason: 'Insufficient permissions to access tasks' 
    };
  }

  /**
   * Filter daily timesheets based on user role
   */
  static filterDailyTimesheets(config: DataFilterConfig): FilteredQueryBuilder {
    const { userRole, userId } = config;
    
    let query = supabase
      .from('daily_timesheets')
      .select('*')
      .order('timesheet_date', { ascending: false });

    // Admin and program officers can see all timesheets
    if (userRole === 'admin' || userRole === 'program_officer') {
      return { query, hasAccess: true };
    }

    // Field staff can only see their own timesheets
    if (userRole === 'field_staff') {
      query = query.eq('staff_id', userId);
      return { 
        query, 
        hasAccess: true,
        reason: 'Field staff can only access their own timesheets'
      };
    }

    return { 
      query, 
      hasAccess: false, 
      reason: 'Insufficient permissions to access timesheet data' 
    };
  }

  /**
   * Filter profiles/staff data based on user role
   */
  static filterProfiles(config: DataFilterConfig): FilteredQueryBuilder {
    const { userRole, userId } = config;
    
    let query = supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });

    // Admin can see all profiles
    if (userRole === 'admin') {
      return { query, hasAccess: true };
    }

    // Program officers can see field staff profiles
    if (userRole === 'program_officer') {
      query = query.in('role', ['field_staff', 'program_officer']);
      return { 
        query, 
        hasAccess: true,
        reason: 'Program officers can access field staff and program officer profiles'
      };
    }

    // Field staff can only see their own profile
    if (userRole === 'field_staff') {
      query = query.eq('id', userId);
      return { 
        query, 
        hasAccess: true,
        reason: 'Field staff can only access their own profile'
      };
    }

    return { 
      query, 
      hasAccess: false, 
      reason: 'Insufficient permissions to access profile data' 
    };
  }

  /**
   * Filter schools based on user role and assignments using RPC function
   */
  static filterSchools(config: DataFilterConfig): FilteredQueryBuilder {
    const { userRole, userId, targetUserId } = config;

    // Use the server-side filtering RPC function
    const query = supabase.rpc('get_assigned_schools', {
      p_staff_id: targetUserId || userId
    });

    return {
      query,
      hasAccess: true,
      reason: 'Using server-side filtering for schools based on assignments'
    };
  }
}

/**
 * Hook for applying data filtering based on current user context
 */
export function useDataFiltering() {
  const { user, profile } = useAuth();

  const getFilterConfig = (): DataFilterConfig | null => {
    if (!user || !profile?.role) return null;
    
    return {
      userRole: profile.role as UserRole,
      userId: user.id,
    };
  };

  const filterFieldReports = (targetUserId?: string, options?: {
    schoolId?: string;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
  }) => {
    const config = getFilterConfig();
    if (!config) return { query: null, hasAccess: false, reason: 'User not authenticated' };

    return DataFilterService.filterFieldReports({ ...config, targetUserId }, options);
  };

  const filterFieldStaffAttendance = (targetUserId?: string, options?: {
    dateFrom?: string;
    dateTo?: string;
  }) => {
    const config = getFilterConfig();
    if (!config) return { query: null, hasAccess: false, reason: 'User not authenticated' };

    return DataFilterService.filterFieldStaffAttendance({ ...config, targetUserId }, options);
  };

  const filterTasks = (targetUserId?: string) => {
    const config = getFilterConfig();
    if (!config) return { query: null, hasAccess: false, reason: 'User not authenticated' };
    
    return DataFilterService.filterTasks({ ...config, targetUserId });
  };

  const filterDailyTimesheets = (targetUserId?: string) => {
    const config = getFilterConfig();
    if (!config) return { query: null, hasAccess: false, reason: 'User not authenticated' };
    
    return DataFilterService.filterDailyTimesheets({ ...config, targetUserId });
  };

  const filterProfiles = (targetUserId?: string) => {
    const config = getFilterConfig();
    if (!config) return { query: null, hasAccess: false, reason: 'User not authenticated' };
    
    return DataFilterService.filterProfiles({ ...config, targetUserId });
  };

  const filterSchools = (targetUserId?: string) => {
    const config = getFilterConfig();
    if (!config) return { query: null, hasAccess: false, reason: 'User not authenticated' };
    
    return DataFilterService.filterSchools({ ...config, targetUserId });
  };

  return {
    filterFieldReports,
    filterFieldStaffAttendance,
    filterTasks,
    filterDailyTimesheets,
    filterProfiles,
    filterSchools,
    currentConfig: getFilterConfig(),
  };
}
