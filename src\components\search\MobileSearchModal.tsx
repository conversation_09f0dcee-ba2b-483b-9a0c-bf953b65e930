import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Search, X } from 'lucide-react';
import { useRoleAwareSearch, SearchResult } from '@/hooks/useRoleAwareSearch';
import SearchResults from './SearchResults';
import { useDebounce } from '@/hooks/useDebounce';

interface MobileSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigate?: (route: string) => void;
}

const MobileSearchModal: React.FC<MobileSearchModalProps> = ({
  isOpen,
  onClose,
  onNavigate
}) => {
  const [query, setQuery] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounce search query
  const debouncedQuery = useDebounce(query, 300);

  // Use the role-aware search hook
  const { data: results = [], isLoading } = useRoleAwareSearch({
    query: debouncedQuery,
    enabled: isOpen && debouncedQuery.length >= 2
  });

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Clear search when modal closes
  useEffect(() => {
    if (!isOpen) {
      setQuery('');
    }
  }, [isOpen]);

  const handleResultClick = (result: SearchResult) => {
    onClose();
    
    if (onNavigate && result.url) {
      onNavigate(result.url);
    }
  };

  const handleClearSearch = () => {
    setQuery('');
    inputRef.current?.focus();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg p-0 gap-0">
        <DialogHeader className="p-4 pb-0">
          <DialogTitle className="sr-only">Search</DialogTitle>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              ref={inputRef}
              type="text"
              placeholder="Search schools, tasks, reports..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10 pr-10 text-base"
            />
            
            {/* Clear button */}
            {query && (
              <button
                onClick={handleClearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </DialogHeader>

        {/* Search Results */}
        <div className="relative max-h-96 overflow-hidden">
          {query.length >= 2 ? (
            <div className="p-4 pt-2">
              <div className="space-y-2">
                {isLoading ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-300 mx-auto mb-2"></div>
                    <p className="text-sm">Searching...</p>
                  </div>
                ) : results.length > 0 ? (
                  <div className="space-y-1">
                    <p className="text-xs text-gray-500 mb-3">
                      {results.length} result{results.length !== 1 ? 's' : ''} found
                    </p>
                    <div className="max-h-80 overflow-y-auto space-y-1">
                      {results.map((result, index) => (
                        <div
                          key={`${result.type}-${result.id}-${index}`}
                          className="p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                          onClick={() => handleResultClick(result)}
                        >
                          <div className="flex items-start gap-3">
                            <div className="flex-1 min-w-0">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {result.title}
                              </h4>
                              {result.description && (
                                <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                  {result.description}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No results found for "{query}"</p>
                    <p className="text-xs text-gray-400 mt-1">
                      Try different keywords
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="p-4 pt-2">
              <div className="text-center py-8 text-gray-500">
                <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Type at least 2 characters to search</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MobileSearchModal;
