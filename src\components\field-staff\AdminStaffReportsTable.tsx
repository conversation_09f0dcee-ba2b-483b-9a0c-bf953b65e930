import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Eye,
  Edit,
  Trash2,
  Search,
  XCircle,
  Download,
  RefreshCw,
  FileText
} from 'lucide-react';
import { useFieldReports, useDeleteFieldReport } from '@/hooks/field-staff/useFieldReports';
import { useSchools } from '@/hooks/useSchools';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import FieldReportDetailsModal from './FieldReportDetailsModal';
import FieldReportEditModal from './FieldReportEditModal';

const AdminStaffReportsTable: React.FC = () => {
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const [editingReportId, setEditingReportId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSchool, setSelectedSchool] = useState<string>('all');
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>('all');

  const { toast } = useToast();
  const { data: schools } = useSchools();
  const { data: reports, isLoading, error, refetch } = useFieldReports({
    schoolId: selectedSchool && selectedSchool !== 'all' ? selectedSchool : undefined,
    dateFrom: dateFilter || undefined,
    limit: 100
  });

  const deleteReportMutation = useDeleteFieldReport();

  // Get unique staff members from reports
  const staffMembers = React.useMemo(() => {
    if (!reports) return [];
    const uniqueStaff = Array.from(new Set(reports.map(r => r.staff_name)))
      .filter(Boolean)
      .sort();
    return uniqueStaff;
  }, [reports]);

  // Get unique activity types
  const activityTypes = React.useMemo(() => {
    if (!reports) return [];
    const uniqueTypes = Array.from(new Set(reports.map(r => r.activity_type)))
      .filter(Boolean)
      .sort();
    return uniqueTypes;
  }, [reports]);

  // Filter reports based on all criteria
  const filteredReports = reports?.filter(report => {
    const matchesSearch = !searchTerm ||
      report.school_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.staff_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.activity_type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSchool = !selectedSchool || selectedSchool === 'all' || report.school_id === selectedSchool;
    const matchesStaff = !selectedStaff || selectedStaff === 'all' || report.staff_name === selectedStaff;
    const matchesActivityType = !activityTypeFilter || activityTypeFilter === 'all' || report.activity_type === activityTypeFilter;

    return matchesSearch && matchesSchool && matchesStaff && matchesActivityType;
  }) || [];

  const handleDeleteReport = async (reportId: string, reportTitle: string) => {
    if (!confirm(`Are you sure you want to delete the report "${reportTitle}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteReportMutation.mutateAsync(reportId);
      toast({
        title: "Report Deleted",
        description: "The field report has been successfully deleted.",
      });
      refetch();
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete the field report. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatActivityType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getActivityTypeBadgeColor = (type: string) => {
    const colors = {
      'round_table_session': 'bg-blue-100 text-blue-800',
      'school_visit': 'bg-green-100 text-green-800',
      'meeting': 'bg-purple-100 text-purple-800',
      'assessment': 'bg-orange-100 text-orange-800',
      'other': 'bg-gray-100 text-gray-800',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedSchool('all');
    setSelectedStaff('all');
    setDateFilter('');
    setActivityTypeFilter('all');
  };

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <XCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">Failed to load field reports</p>
          <Button onClick={() => refetch()} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters - Streamlined layout */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedSchool} onValueChange={setSelectedSchool}>
              <SelectTrigger>
                <SelectValue placeholder="All schools" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All schools</SelectItem>
                {schools?.map(school => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStaff} onValueChange={setSelectedStaff}>
              <SelectTrigger>
                <SelectValue placeholder="All staff" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All staff</SelectItem>
                {staffMembers.map(staff => (
                  <SelectItem key={staff} value={staff}>
                    {staff}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={activityTypeFilter} onValueChange={setActivityTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                {activityTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {formatActivityType(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              placeholder="Date from"
            />
          </div>

          {/* Clear filters button - only show if filters are active */}
          {(searchTerm || selectedSchool !== 'all' || selectedStaff !== 'all' || activityTypeFilter !== 'all' || dateFilter) && (
            <div className="mt-4 flex justify-end">
              <Button variant="outline" onClick={clearFilters} size="sm">
                Clear Filters
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reports Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Reports ({filteredReports.length})</h3>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => refetch()} disabled={isLoading} size="sm">
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 mx-auto mb-4 animate-spin text-gray-400" />
              <p>Loading reports...</p>
            </div>
          ) : filteredReports.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No field reports found</p>
              <p className="text-sm mt-2">
                {searchTerm || (selectedSchool && selectedSchool !== 'all') || (selectedStaff && selectedStaff !== 'all') || dateFilter || (activityTypeFilter && activityTypeFilter !== 'all')
                  ? 'Try adjusting your filters'
                  : 'Field reports will appear here when staff submit them'
                }
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Staff Member</TableHead>
                    <TableHead>School</TableHead>
                    <TableHead>Activity Type</TableHead>
                    <TableHead>Sessions</TableHead>
                    <TableHead>Students</TableHead>
                    <TableHead>Follow-up</TableHead>
                    <TableHead className="w-[140px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReports.map(report => (
                    <TableRow key={report.id} className="hover:bg-gray-50">
                      <TableCell>
                        {report.report_date ? format(new Date(report.report_date), 'MMM dd, yyyy') : 'N/A'}
                      </TableCell>
                      <TableCell className="font-medium">
                        {report.staff_name}
                      </TableCell>
                      <TableCell>
                        {report.school_name}
                      </TableCell>
                      <TableCell>
                        <Badge className={getActivityTypeBadgeColor(report.activity_type)}>
                          {formatActivityType(report.activity_type)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {report.total_round_tables_calculated || report.round_table_sessions_count || 0}
                      </TableCell>
                      <TableCell>
                        {report.total_students_calculated || report.total_students_attended || 0}
                      </TableCell>
                      <TableCell>
                        {report.follow_up_required ? (
                          <Badge variant="outline" className="text-orange-600 border-orange-600">
                            Required
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            None
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedReportId(report.id)}
                            title="View Report"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingReportId(report.id)}
                            title="Edit Report"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteReport(report.id, `${report.school_name} - ${formatActivityType(report.activity_type)}`)}
                            title="Delete Report"
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Report Modal */}
      {selectedReportId && (
        <FieldReportDetailsModal
          reportId={selectedReportId}
          isOpen={!!selectedReportId}
          onClose={() => setSelectedReportId(null)}
        />
      )}

      {/* Edit Report Modal */}
      {editingReportId && (
        <FieldReportEditModal
          reportId={editingReportId}
          isOpen={!!editingReportId}
          onClose={() => setEditingReportId(null)}
          onSave={() => {
            setEditingReportId(null);
            refetch();
          }}
        />
      )}
    </div>
  );
};

export default AdminStaffReportsTable;
