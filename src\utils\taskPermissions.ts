import { UserRole } from '@/utils/rbac';

/**
 * Task Assignment Permissions and Logic
 * 
 * This module centralizes all task assignment logic and permissions
 * to ensure consistent behavior across the application.
 */

export interface TaskAssignmentRules {
  canCreateTasks: boolean;
  canAssignToOthers: boolean;
  canAssignToSelf: boolean;
  canViewAllTasks: boolean;
  canViewOwnTasks: boolean;
  canViewAssignedTasks: boolean;
  canUpdateOwnTasks: boolean;
  canUpdateAssignedTasks: boolean;
  canUpdateAnyTask: boolean;
  defaultAssignee: 'self' | 'unassigned' | null;
}

/**
 * Get task assignment rules for a specific user role
 */
export function getTaskAssignmentRules(userRole?: UserRole | null): TaskAssignmentRules {
  switch (userRole) {
    case 'admin':
      return {
        canCreateTasks: true,
        canAssignToOthers: true,
        canAssignToSelf: true,
        canViewAllTasks: true,
        canViewOwnTasks: true,
        canViewAssignedTasks: true,
        canUpdateOwnTasks: true,
        canUpdateAssignedTasks: true,
        canUpdateAnyTask: true,
        defaultAssignee: 'unassigned', // Admin can choose who to assign to
      };

    case 'program_officer':
      return {
        canCreateTasks: true,
        canAssignToOthers: true,
        canAssignToSelf: true,
        canViewAllTasks: true,
        canViewOwnTasks: true,
        canViewAssignedTasks: true,
        canUpdateOwnTasks: true,
        canUpdateAssignedTasks: true,
        canUpdateAnyTask: true,
        defaultAssignee: 'unassigned', // Program officer can choose who to assign to
      };

    case 'field_staff':
      return {
        canCreateTasks: true,
        canAssignToOthers: false,
        canAssignToSelf: true,
        canViewAllTasks: false,
        canViewOwnTasks: true,
        canViewAssignedTasks: true,
        canUpdateOwnTasks: true,
        canUpdateAssignedTasks: true,
        canUpdateAnyTask: false,
        defaultAssignee: 'self', // Field staff can only assign to themselves
      };

    default:
      return {
        canCreateTasks: false,
        canAssignToOthers: false,
        canAssignToSelf: false,
        canViewAllTasks: false,
        canViewOwnTasks: false,
        canViewAssignedTasks: false,
        canUpdateOwnTasks: false,
        canUpdateAssignedTasks: false,
        canUpdateAnyTask: false,
        defaultAssignee: null,
      };
  }
}

/**
 * Determine the final assignee for a task based on user role and input
 */
export function resolveTaskAssignee(
  userRole: UserRole | null | undefined,
  userId: string | null | undefined,
  requestedAssignee: string | null | undefined
): string | null {
  const rules = getTaskAssignmentRules(userRole);

  // If user can't create tasks, return null
  if (!rules.canCreateTasks || !userId) {
    return null;
  }

  // Field staff can only assign to themselves
  if (userRole === 'field_staff') {
    return userId;
  }

  // Admin and program officers can assign to others
  if (rules.canAssignToOthers) {
    // If no assignee specified or self-assigned, assign to creator
    if (!requestedAssignee || requestedAssignee === userId) {
      return userId;
    }
    // Otherwise, use the requested assignee
    return requestedAssignee;
  }

  // Default to self-assignment
  return userId;
}

/**
 * Check if a user can assign a task to a specific person
 */
export function canAssignTaskTo(
  userRole: UserRole | null | undefined,
  userId: string | null | undefined,
  targetUserId: string | null | undefined
): boolean {
  const rules = getTaskAssignmentRules(userRole);

  if (!rules.canCreateTasks || !userId) {
    return false;
  }

  // Can always assign to self
  if (targetUserId === userId) {
    return true;
  }

  // Can assign to others only if role allows it
  return rules.canAssignToOthers;
}

/**
 * Check if a user can view a specific task
 */
export function canViewTask(
  userRole: UserRole | null | undefined,
  userId: string | null | undefined,
  task: {
    created_by?: string | null;
    assigned_to?: string | null;
  }
): boolean {
  const rules = getTaskAssignmentRules(userRole);

  if (!userId) {
    return false;
  }

  // Admin and program officers can view all tasks
  if (rules.canViewAllTasks) {
    return true;
  }

  // Can view tasks created by user
  if (rules.canViewOwnTasks && task.created_by === userId) {
    return true;
  }

  // Can view tasks assigned to user
  if (rules.canViewAssignedTasks && task.assigned_to === userId) {
    return true;
  }

  return false;
}

/**
 * Check if a user can update a specific task
 */
export function canUpdateTask(
  userRole: UserRole | null | undefined,
  userId: string | null | undefined,
  task: {
    created_by?: string | null;
    assigned_to?: string | null;
  }
): boolean {
  const rules = getTaskAssignmentRules(userRole);

  if (!userId) {
    return false;
  }

  // Admin and program officers can update any task
  if (rules.canUpdateAnyTask) {
    return true;
  }

  // Can update tasks created by user
  if (rules.canUpdateOwnTasks && task.created_by === userId) {
    return true;
  }

  // Can update tasks assigned to user
  if (rules.canUpdateAssignedTasks && task.assigned_to === userId) {
    return true;
  }

  return false;
}

/**
 * Get user-friendly description of task assignment permissions
 */
export function getTaskPermissionDescription(userRole?: UserRole | null): string {
  switch (userRole) {
    case 'admin':
      return 'As an administrator, you can create tasks and assign them to any user, including yourself.';
    
    case 'program_officer':
      return 'As a program officer, you can create tasks and assign them to any field staff member or yourself.';
    
    case 'field_staff':
      return 'As field staff, you can create tasks that are automatically assigned to yourself.';
    
    default:
      return 'You do not have permission to create tasks.';
  }
}

/**
 * Task Assignment Constants
 */
export const TASK_ASSIGNMENT_CONSTANTS = {
  SELF_ASSIGNMENT_VALUE: 'self',
  UNASSIGNED_VALUE: 'unassigned',
  NOT_SCHOOL_SPECIFIC_VALUE: 'not_school_specific',
} as const;
