# Role-Aware Search Implementation

## Overview

The iLEAD Field Tracker now includes a comprehensive role-aware search functionality that allows users to search across different types of content based on their role permissions. The search ensures that users only see results they are authorized to access.

## Features

### Global Search
- **Desktop**: Available in the header with keyboard shortcut (Cmd/Ctrl + K)
- **Mobile**: Accessible via search button in header, opens in modal
- **Real-time**: Debounced search with 300ms delay for optimal performance
- **Role-aware**: Results filtered based on user permissions

### Search Types by Role

#### Field Staff
- **Schools**: All active schools
- **Tasks**: Only tasks assigned to them or created by them
- **Field Reports**: Only their own field reports

#### Program Officers
- **Schools**: All active schools
- **Tasks**: All tasks in the system
- **Field Reports**: All field reports from all staff
- **Books**: All books in inventory
- **Users**: All staff members

#### Admins
- **Schools**: All active schools
- **Tasks**: All tasks in the system
- **Field Reports**: All field reports from all staff
- **Books**: All books in inventory
- **Users**: All staff members
- **Distributions**: All book distributions

## Technical Implementation

### Components

1. **GlobalSearch** (`src/components/search/GlobalSearch.tsx`)
   - Main search component for desktop
   - Handles keyboard shortcuts and debounced input
   - Shows dropdown results with navigation

2. **MobileSearchModal** (`src/components/search/MobileSearchModal.tsx`)
   - Modal-based search for mobile devices
   - Touch-optimized interface
   - Auto-focus on open

3. **SearchResults** (`src/components/search/SearchResults.tsx`)
   - Displays search results with type badges
   - Handles result navigation
   - Shows loading and empty states

### Hooks

1. **useRoleAwareSearch** (`src/hooks/useRoleAwareSearch.ts`)
   - Core search logic with role-based filtering
   - Queries multiple data sources based on permissions
   - Returns unified search results

2. **useDebounce** (`src/hooks/useDebounce.ts`)
   - Debounces search input to prevent excessive API calls
   - Configurable delay (default: 300ms)

### Search Result Structure

```typescript
interface SearchResult {
  id: string;
  type: 'school' | 'task' | 'field_report' | 'book' | 'user' | 'distribution';
  title: string;
  description?: string;
  metadata?: Record<string, any>;
  url?: string;
  relevance?: number;
}
```

## Security Features

### Role-Based Access Control
- Search types are filtered based on user role before querying
- Database queries include role-specific WHERE clauses
- Field staff can only see their own data for sensitive content

### Data Filtering
- **Field Staff**: 
  - Tasks: `assigned_to = user_id OR created_by = user_id`
  - Field Reports: `staff_id = user_id`
- **Program Officers & Admins**: 
  - Full access to all data within their permitted types

### Query Optimization
- Searches are limited to prevent performance issues
- Results are paginated (max 20 per search)
- Debounced input reduces server load

## Usage Examples

### Basic Search
```typescript
import { GlobalSearch } from '@/components/search';

<GlobalSearch 
  onNavigate={(route) => navigate(route)}
  placeholder="Search schools, tasks, reports..."
/>
```

### Mobile Search
```typescript
import { MobileSearchModal } from '@/components/search';

<MobileSearchModal
  isOpen={isSearchOpen}
  onClose={() => setIsSearchOpen(false)}
  onNavigate={(route) => navigate(route)}
/>
```

### Custom Search Hook
```typescript
import { useRoleAwareSearch } from '@/hooks/useRoleAwareSearch';

const { data: results, isLoading } = useRoleAwareSearch({
  query: searchTerm,
  filters: { types: ['school', 'task'] },
  limit: 10
});
```

## Performance Considerations

- **Debouncing**: 300ms delay prevents excessive API calls
- **Caching**: Results cached for 5 minutes using React Query
- **Pagination**: Limited to 20 results per search
- **Stale Time**: 30 seconds to balance freshness and performance

## Future Enhancements

1. **Advanced Filters**: Date ranges, status filters, etc.
2. **Search History**: Recent searches for quick access
3. **Saved Searches**: Bookmark frequently used searches
4. **Full-text Search**: Enhanced search across content fields
5. **Search Analytics**: Track popular searches and improve results
