# Role-Aware In-App Notification System

## Overview

The iLEAD Field Tracker includes a comprehensive role-aware in-app notification system that ensures users receive relevant notifications based on their role and permissions. The system provides real-time updates about tasks, field reports, system changes, and other important events.

## Features

### 🔔 **Notification Bell**
- **Real-time badge**: Shows unread notification count
- **Visual indicators**: Animated bell icon for new notifications
- **Dropdown preview**: Quick access to recent notifications
- **Role-aware content**: Only shows notifications relevant to user's role

### 📱 **Notification Types**

#### **Task Notifications**
- `task_assigned`: When a task is assigned to a user
- `task_completed`: When a task is marked as completed
- `task_overdue`: When a task becomes overdue

#### **Field Report Notifications**
- `field_report_submitted`: When a field staff submits a report
- `field_report_approved`: When a report is approved
- `field_report_rejected`: When a report needs revision

#### **System Notifications**
- `system_update`: System maintenance and feature updates
- `weekly_summary`: Weekly activity summaries
- `monthly_report`: Monthly performance reports

#### **Inventory Notifications**
- `book_distribution_created`: When books are distributed
- `low_inventory`: When book inventory is running low

#### **User Management Notifications**
- `user_created`: When a new user is added
- `school_added`: When a new school is registered

#### **Reminder Notifications**
- `check_in_reminder`: Daily check-in reminders for field staff
- `session_starting`: Session start notifications

### 🎯 **Role-Based Access Control**

#### **Field Staff** receive notifications about:
- ✅ Tasks assigned to them
- ✅ Their field report status updates
- ✅ Check-in reminders
- ✅ Session notifications for their schools
- ✅ System updates

#### **Program Officers** receive notifications about:
- ✅ All field reports submitted by staff
- ✅ Tasks assigned to them
- ✅ New schools added
- ✅ Book distributions
- ✅ Low inventory alerts
- ✅ System updates

#### **Admins** receive notifications about:
- ✅ All system activities
- ✅ User management changes
- ✅ All field reports and tasks
- ✅ Inventory and distribution updates
- ✅ System maintenance notifications

## Technical Implementation

### Database Schema

```sql
-- Main notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY,
    type notification_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority notification_priority DEFAULT 'normal',
    status notification_status DEFAULT 'unread',
    recipient_id UUID NOT NULL,
    recipient_role VARCHAR(50) NOT NULL,
    sender_id UUID,
    sender_role VARCHAR(50),
    related_entity_type VARCHAR(50),
    related_entity_id UUID,
    metadata JSONB DEFAULT '{}',
    action_url VARCHAR(500),
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    archived_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);
```

### Core Components

1. **NotificationBell** (`src/components/notifications/NotificationBell.tsx`)
   - Header notification icon with badge
   - Dropdown with recent notifications
   - Real-time unread count updates

2. **NotificationDropdown** (`src/components/notifications/NotificationDropdown.tsx`)
   - Displays recent notifications in dropdown
   - Mark as read functionality
   - Archive notifications
   - Navigate to related content

3. **NotificationsPage** (`src/components/notifications/NotificationsPage.tsx`)
   - Full notifications management interface
   - Filter by type, status, and search
   - Bulk actions (mark all read, archive)
   - Tabbed view (all, unread, archived)

### Core Hooks

1. **useNotifications** (`src/hooks/useNotifications.ts`)
   - Fetch notifications with filtering
   - Real-time updates via React Query
   - Role-based data filtering

2. **useUnreadNotificationCount** (`src/hooks/useNotifications.ts`)
   - Get real-time unread count
   - Auto-refresh every 30 seconds
   - Used for notification badge

3. **useNotificationTriggers** (`src/hooks/useNotificationTriggers.ts`)
   - Automatic notification creation
   - Integrates with app mutations
   - Manual trigger functions

### Notification Service

**NotificationService** (`src/services/notificationService.ts`)
- Role-aware notification creation
- Template-based messaging
- Bulk notification sending
- Integration with app events

## Usage Examples

### Creating Notifications

```typescript
import { NotificationService } from '@/services/notificationService';

// Notify when task is assigned
await NotificationService.notifyTaskAssigned(
  taskId,
  taskTitle,
  assignedToId,
  assignedByName,
  assignedById,
  assignedByRole
);

// Notify about field report submission
await NotificationService.notifyFieldReportSubmitted(
  reportId,
  schoolName,
  staffName,
  staffId
);
```

### Using Notification Hooks

```typescript
import { useNotifications, useUnreadNotificationCount } from '@/hooks/useNotifications';

// Get notifications
const { data: notifications } = useNotifications({
  limit: 10,
  unreadOnly: false
});

// Get unread count
const { data: unreadCount } = useUnreadNotificationCount();
```

### Automatic Triggers

```typescript
import { useNotificationTriggers } from '@/hooks/useNotificationTriggers';

const triggers = useNotificationTriggers();

// Manual trigger
await triggers.notifyTaskAssigned(taskId, taskTitle, assignedToId);
```

## Security Features

### Row Level Security (RLS)
- Users can only see their own notifications
- Database-level access control
- Prevents unauthorized access

### Role-Based Filtering
- Notifications filtered by recipient role
- Content tailored to user permissions
- No sensitive information exposure

### Data Validation
- Input sanitization for all notification content
- XSS prevention in notification messages
- Secure URL handling for action links

## Performance Optimizations

### Caching Strategy
- **React Query**: 5-minute cache with 30-second stale time
- **Unread Count**: 1-minute cache with 30-second refresh
- **Real-time Updates**: Automatic cache invalidation

### Database Optimization
- **Indexes**: Optimized for common queries
- **Cleanup**: Automatic expired notification removal
- **Pagination**: Limited results to prevent performance issues

### Efficient Queries
- **Role-based filtering**: Database-level WHERE clauses
- **Batch operations**: Bulk notification creation
- **Selective updates**: Only update necessary fields

## Future Enhancements

1. **Push Notifications**: Browser push notification support
2. **Email Integration**: Email notification delivery
3. **SMS Notifications**: SMS delivery for urgent notifications
4. **Notification Preferences**: User-customizable notification settings
5. **Rich Content**: Support for images and rich text in notifications
6. **Notification Templates**: Advanced templating system
7. **Analytics**: Notification engagement tracking
8. **Scheduling**: Delayed and scheduled notifications
