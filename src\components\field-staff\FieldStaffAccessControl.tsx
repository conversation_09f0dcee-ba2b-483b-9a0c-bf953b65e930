import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle } from 'lucide-react';

interface FieldStaffAccessControlProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  allowSelfAccess?: boolean;
  targetUserId?: string;
  fallbackMessage?: string;
}

/**
 * Component to control access to field staff features based on user roles
 * and ownership of data
 */
const FieldStaffAccessControl: React.FC<FieldStaffAccessControlProps> = ({
  children,
  requiredRoles = ['admin', 'program_officer', 'field_staff'],
  allowSelfAccess = true,
  targetUserId,
  fallbackMessage = 'You do not have permission to access this feature.'
}) => {
  // Call all hooks at the top level before any conditional logic
  const { user, profile } = useAuth();
  const { roleChecker } = useAccessControl();

  // Early return for authentication check
  if (!user || !profile) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Please log in to access this feature.
        </AlertDescription>
      </Alert>
    );
  }

  // Check if user has required role
  const hasRequiredRole = requiredRoles.includes(profile.role || '');

  // Check if user is accessing their own data
  const isOwnData = allowSelfAccess && targetUserId && user.id === targetUserId;

  // Admin and program officers can access all data
  const isAdminOrProgramOfficer = roleChecker.isAdminOrProgramOfficer();

  // Field staff can only access their own data
  const isFieldStaffWithOwnData = profile.role === 'field_staff' && (isOwnData || !targetUserId);

  const hasAccess = hasRequiredRole && (isAdminOrProgramOfficer || isFieldStaffWithOwnData);

  if (!hasAccess) {
    return (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          {fallbackMessage}
        </AlertDescription>
      </Alert>
    );
  }

  return <>{children}</>;
};





/**
 * Component to show different content based on user role
 */
interface RoleBasedContentProps {
  adminContent?: React.ReactNode;
  programOfficerContent?: React.ReactNode;
  fieldStaffContent?: React.ReactNode;
  defaultContent?: React.ReactNode;
}

export const RoleBasedContent: React.FC<RoleBasedContentProps> = ({
  adminContent,
  programOfficerContent,
  fieldStaffContent,
  defaultContent
}) => {
  const { currentUser } = useAccessControl();

  if (!currentUser) {
    return <>{defaultContent}</>;
  }

  switch (currentUser.role) {
    case 'admin':
      return <>{adminContent || defaultContent}</>;
    case 'program_officer':
      return <>{programOfficerContent || defaultContent}</>;
    case 'field_staff':
      return <>{fieldStaffContent || defaultContent}</>;
    default:
      return <>{defaultContent}</>;
  }
};

export default FieldStaffAccessControl;
