import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { LucideIcon, MoreHorizontal, ChevronDown } from 'lucide-react';

export interface PageHeaderAction {
  label: string;
  onClick: () => void;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  className?: string;
  disabled?: boolean;
  loading?: boolean;
  tooltip?: string;
  priority?: 'primary' | 'secondary' | 'tertiary'; // For responsive behavior
  mobileHidden?: boolean; // Hide on mobile
  desktopHidden?: boolean; // Hide on desktop
}

export interface PageHeaderBreadcrumb {
  label: string;
  href?: string;
  onClick?: () => void;
}

export interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  actions?: PageHeaderAction[];
  children?: React.ReactNode;
  className?: string;
  breadcrumbs?: PageHeaderBreadcrumb[];
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  mobileOptimized?: boolean;
  maxActions?: number; // Max actions before overflow menu
  actionLayout?: 'inline' | 'stacked' | 'responsive'; // Layout strategy
}

/**
 * Enhanced page header component with standardized action button placement,
 * responsive behavior, and mobile optimization.
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  icon: Icon,
  actions = [],
  children,
  className,
  breadcrumbs = [],
  badge,
  mobileOptimized = true,
  maxActions = 3,
  actionLayout = 'responsive'
}) => {
  // Filter actions based on device visibility
  const visibleActions = actions.filter(action => {
    if (typeof window !== 'undefined') {
      const isMobile = window.innerWidth < 768;
      if (isMobile && action.mobileHidden) return false;
      if (!isMobile && action.desktopHidden) return false;
    }
    return true;
  });

  // Sort actions by priority
  const sortedActions = [...visibleActions].sort((a, b) => {
    const priorityOrder = { primary: 0, secondary: 1, tertiary: 2 };
    const aPriority = priorityOrder[a.priority || 'secondary'];
    const bPriority = priorityOrder[b.priority || 'secondary'];
    return aPriority - bPriority;
  });

  // Split actions for overflow menu
  const primaryActions = sortedActions.slice(0, maxActions);
  const overflowActions = sortedActions.slice(maxActions);

  const renderAction = (action: PageHeaderAction, index: number, isInDropdown = false) => {
    const ActionIcon = action.icon;
    const buttonContent = (
      <>
        {ActionIcon && <ActionIcon className="h-4 w-4 mr-2" />}
        {action.label}
      </>
    );

    if (isInDropdown) {
      return (
        <DropdownMenuItem
          key={index}
          onClick={action.onClick}
          disabled={action.disabled || action.loading}
          className="cursor-pointer"
        >
          {buttonContent}
        </DropdownMenuItem>
      );
    }

    return (
      <Button
        key={index}
        onClick={action.onClick}
        variant={action.variant || 'default'}
        disabled={action.disabled || action.loading}
        className={cn(
          // Default styling
          action.variant === 'default' && "bg-ilead-green hover:bg-ilead-dark-green text-white",
          // Mobile optimization
          mobileOptimized && "btn-mobile",
          // Priority-based styling
          action.priority === 'primary' && "font-semibold",
          action.priority === 'tertiary' && "text-sm",
          // Custom className
          action.className
        )}
        title={action.tooltip}
      >
        {action.loading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            Loading...
          </>
        ) : (
          buttonContent
        )}
      </Button>
    );
  };

  return (
    <div className={cn(
      "space-y-4",
      className
    )}>
      {/* Breadcrumbs */}
      {breadcrumbs.length > 0 && (
        <nav className="flex items-center space-x-1 text-sm text-gray-500">
          {breadcrumbs.map((crumb, index) => (
            <span key={index} className="flex items-center">
              {index > 0 && <span className="mx-1">/</span>}
              {crumb.href || crumb.onClick ? (
                <button
                  onClick={crumb.onClick}
                  className="hover:text-gray-700 transition-colors"
                >
                  {crumb.label}
                </button>
              ) : (
                <span className="text-gray-900 font-medium">{crumb.label}</span>
              )}
            </span>
          ))}
        </nav>
      )}

      {/* Main Header */}
      <div className={cn(
        "flex flex-col gap-4",
        actionLayout === 'inline' && "sm:flex-row sm:items-center sm:justify-between",
        actionLayout === 'responsive' && "lg:flex-row lg:items-center lg:justify-between"
      )}>
        {/* Title Section */}
        <div className="flex items-start gap-3 min-w-0 flex-1">
          {Icon && (
            <Icon className="h-6 w-6 text-ilead-green flex-shrink-0 mt-1" />
          )}
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2 flex-wrap">
              <h1 className={cn(
                "font-bold text-gray-900 leading-tight",
                mobileOptimized ? "text-xl sm:text-2xl lg:text-3xl" : "text-2xl sm:text-3xl"
              )}>
                {title}
              </h1>
              {badge && (
                <Badge variant={badge.variant || 'default'} className="text-xs">
                  {badge.text}
                </Badge>
              )}
            </div>
            {description && (
              <p className={cn(
                "text-gray-600 mt-1",
                mobileOptimized ? "text-sm sm:text-base" : "text-sm sm:text-base"
              )}>
                {description}
              </p>
            )}
          </div>
        </div>

        {/* Actions Section */}
        {(sortedActions.length > 0 || children) && (
          <div className={cn(
            "flex items-center gap-2 flex-shrink-0",
            actionLayout === 'stacked' && "flex-col items-stretch",
            actionLayout === 'responsive' && "flex-wrap sm:flex-nowrap",
            mobileOptimized && "w-full sm:w-auto"
          )}>
            {/* Primary Actions */}
            <div className={cn(
              "flex items-center gap-2",
              actionLayout === 'stacked' && "flex-col w-full",
              mobileOptimized && actionLayout !== 'stacked' && "flex-wrap sm:flex-nowrap"
            )}>
              {primaryActions.map((action, index) => renderAction(action, index))}

              {/* Overflow Menu */}
              {overflowActions.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn(
                        mobileOptimized && "btn-mobile"
                      )}
                    >
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">More actions</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    {overflowActions.map((action, index) =>
                      renderAction(action, index + primaryActions.length, true)
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>

            {/* Custom Children */}
            {children && (
              <div className={cn(
                actionLayout === 'stacked' && "w-full",
                "flex items-center gap-2"
              )}>
                {children}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PageHeader;
