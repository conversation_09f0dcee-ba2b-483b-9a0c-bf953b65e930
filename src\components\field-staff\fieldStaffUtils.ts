import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';

/**
 * Hook to check field staff access permissions
 * @deprecated Use useAccessControl().roleChecker instead for centralized role checking
 */
export const useFieldStaffAccess = () => {
  const { user, profile } = useAuth();
  const { roleChecker } = useAccessControl();

  const canViewAllStaffData = roleChecker.canViewAllStaffData();
  const canViewOwnData = roleChecker.isFieldStaff() || roleChecker.isAdminOrProgramOfficer();
  const canManageTimesheets = roleChecker.canManageTimesheets();
  const canApproveReports = roleChecker.canApproveReports();
  const canCheckInOut = roleChecker.canCheckInOut();

  const canAccessStaffData = (targetUserId?: string) => {
    if (!user || !profile) return false;

    // Admin and program officers can access all data
    if (profile.role === 'admin' || profile.role === 'program_officer') {
      return true;
    }

    // Field staff can only access their own data
    if (profile.role === 'field_staff') {
      return !targetUserId || user.id === targetUserId;
    }

    return false;
  };

  return {
    canViewAllStaffData,
    canViewOwnData,
    canManageTimesheets,
    canApproveReports,
    canCheckInOut,
    canAccessStaffData,
    userRole: profile?.role,
    userId: user?.id,
  };
};
