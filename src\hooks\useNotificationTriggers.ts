import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { NotificationService } from '@/services/notificationService';

/**
 * Hook that sets up automatic notification triggers for various app events
 * This integrates with existing functionality to send role-aware notifications
 */
export const useNotificationTriggers = () => {
  const { user, profile } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user || !profile) return;

    // Set up query cache listeners for automatic notifications
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event.type === 'updated' && event.query.state.data) {
        handleQueryUpdate(event);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [user, profile, queryClient]);

  const handleQueryUpdate = async (event: any) => {
    const queryKey = event.query.queryKey;
    const data = event.query.state.data;

    try {
      // Handle task updates
      if (queryKey[0] === 'tasks' && queryKey[1] === 'create') {
        const task = data;
        if (task.assigned_to && task.assigned_to !== user?.id) {
          await NotificationService.notifyTaskAssigned(
            task.id,
            task.title,
            task.assigned_to,
            profile?.name || 'Unknown',
            user?.id,
            profile?.role
          );
        }
      }

      // Handle field report submissions
      if (queryKey[0] === 'field-reports' && queryKey[1] === 'create') {
        const report = data;
        if (profile?.role === 'field_staff') {
          await NotificationService.notifyFieldReportSubmitted(
            report.id,
            report.school_name || 'Unknown School',
            profile.name || 'Unknown Staff',
            user?.id || ''
          );
        }
      }

      // Handle school creation
      if (queryKey[0] === 'schools' && queryKey[1] === 'create') {
        const school = data;
        await NotificationService.notifySchoolAdded(
          school.id,
          school.name,
          school.district || 'Unknown District',
          user?.id,
          profile?.role
        );
      }

      // Handle user creation
      if (queryKey[0] === 'users' && queryKey[1] === 'create') {
        const newUser = data;
        await NotificationService.notifyUserCreated(
          newUser.id,
          newUser.name || 'Unknown User',
          newUser.role || 'Unknown Role',
          user?.id,
          profile?.role
        );
      }

      // Handle book distribution
      if (queryKey[0] === 'distributions' && queryKey[1] === 'create') {
        const distribution = data;
        await NotificationService.notifyBookDistribution(
          distribution.id,
          distribution.school_name || 'Unknown School',
          distribution.book_title || 'Unknown Book',
          distribution.quantity || 0,
          user?.id,
          profile?.role
        );
      }

    } catch (error) {
      console.error('Error sending notification:', error);
    }
  };

  // Manual notification triggers that can be called from components
  const triggers = {
    // Trigger task assignment notification
    notifyTaskAssigned: async (taskId: string, taskTitle: string, assignedToId: string) => {
      if (!user || !profile) return;
      
      await NotificationService.notifyTaskAssigned(
        taskId,
        taskTitle,
        assignedToId,
        profile.name || 'Unknown',
        user.id,
        profile.role
      );
    },

    // Trigger field report status notification
    notifyFieldReportStatus: async (
      reportId: string,
      schoolName: string,
      staffId: string,
      approved: boolean,
      reason?: string
    ) => {
      if (!user || !profile) return;
      
      await NotificationService.notifyFieldReportStatus(
        reportId,
        schoolName,
        staffId,
        approved,
        reason,
        user.id,
        profile.role
      );
    },

    // Trigger low inventory notification
    notifyLowInventory: async (bookId: string, bookTitle: string, currentQuantity: number, threshold: number) => {
      await NotificationService.notifyLowInventory(bookId, bookTitle, currentQuantity, threshold);
    },

    // Trigger system update notification
    notifySystemUpdate: async (title: string, message: string, targetRoles?: string[]) => {
      await NotificationService.notifySystemUpdate(title, message, targetRoles);
    },

    // Trigger check-in reminder
    notifyCheckInReminder: async (staffId: string) => {
      await NotificationService.notifyCheckInReminder(staffId);
    },
  };

  return triggers;
};

/**
 * Hook for sending notifications when specific mutations succeed
 * This can be used in components that perform actions requiring notifications
 */
export const useNotificationOnMutation = () => {
  const { user, profile } = useAuth();

  const notifyOnSuccess = (type: string, data: any) => {
    if (!user || !profile) return;

    switch (type) {
      case 'task_assigned':
        NotificationService.notifyTaskAssigned(
          data.taskId,
          data.taskTitle,
          data.assignedToId,
          profile.name || 'Unknown',
          user.id,
          profile.role
        );
        break;

      case 'field_report_submitted':
        if (profile.role === 'field_staff') {
          NotificationService.notifyFieldReportSubmitted(
            data.reportId,
            data.schoolName,
            profile.name || 'Unknown Staff',
            user.id
          );
        }
        break;

      case 'field_report_status':
        NotificationService.notifyFieldReportStatus(
          data.reportId,
          data.schoolName,
          data.staffId,
          data.approved,
          data.reason,
          user.id,
          profile.role
        );
        break;

      case 'school_added':
        NotificationService.notifySchoolAdded(
          data.schoolId,
          data.schoolName,
          data.district,
          user.id,
          profile.role
        );
        break;

      case 'user_created':
        NotificationService.notifyUserCreated(
          data.userId,
          data.userName,
          data.userRole,
          user.id,
          profile.role
        );
        break;

      case 'book_distribution':
        NotificationService.notifyBookDistribution(
          data.distributionId,
          data.schoolName,
          data.bookTitle,
          data.quantity,
          user.id,
          profile.role
        );
        break;

      default:
        console.warn('Unknown notification type:', type);
    }
  };

  return { notifyOnSuccess };
};
