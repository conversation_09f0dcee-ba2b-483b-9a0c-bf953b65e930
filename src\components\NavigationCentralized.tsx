import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Menu, X } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';
import { 
  getNavigationForRole, 
  getNavigationSections, 
  type NavigationItem 
} from '@/config/navigation';

interface NavigationProps {
  currentView: string;
  onViewChange: (view: string) => void;
}

const NavigationCentralized = ({ currentView, onViewChange }: NavigationProps) => {
  const [expanded, setExpanded] = useState(true);
  const [openSections, setOpenSections] = useState<string[]>(['main', 'management']);
  const { currentUser } = useAccessControl();

  if (!currentUser) {
    return null;
  }

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const handleItemClick = (item: NavigationItem) => {
    onViewChange(item.route);
  };

  // Get navigation sections organized by role
  const navigationSections = getNavigationSections(currentUser.role);

  return (
    <div className={`bg-white border-r border-gray-200 transition-all duration-300 ${
      expanded ? 'w-64' : 'w-16'
    }`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {expanded && (
            <div>
              <h1 className="text-xl font-bold text-gray-900">iLead</h1>
              <p className="text-sm text-gray-500 capitalize">{currentUser.role.replace('_', ' ')}</p>
            </div>
          )}
          <button
            onClick={() => setExpanded(!expanded)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            {expanded ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </button>
        </div>
      </div>

      {/* Navigation Sections */}
      <div className="p-2 space-y-2">
        {navigationSections.map((section) => (
          <div key={section.id} className="space-y-1">
            {/* Section Header */}
            {expanded && section.items.length > 0 && (
              <div className="px-3 py-2">
                <button
                  onClick={() => toggleSection(section.id)}
                  className="flex items-center justify-between w-full text-left"
                >
                  <span className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    {section.label}
                  </span>
                  {openSections.includes(section.id) ? (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            )}

            {/* Section Items */}
            {(expanded ? openSections.includes(section.id) : true) && (
              <div className="space-y-1">
                {section.items.map((item) => (
                  <NavigationItemComponent
                    key={item.id}
                    item={item}
                    currentView={currentView}
                    onItemClick={handleItemClick}
                    expanded={expanded}
                    level={0}
                  />
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

interface NavigationItemComponentProps {
  item: NavigationItem;
  currentView: string;
  onItemClick: (item: NavigationItem) => void;
  expanded: boolean;
  level: number;
}

const NavigationItemComponent = ({ 
  item, 
  currentView, 
  onItemClick, 
  expanded, 
  level 
}: NavigationItemComponentProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const isActive = currentView === item.route;
  const indentClass = level > 0 ? 'ml-4' : '';

  const handleClick = () => {
    if (hasChildren) {
      setIsOpen(!isOpen);
    } else {
      onItemClick(item);
    }
  };

  return (
    <div className={indentClass}>
      <button
        onClick={handleClick}
        className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-left transition-colors ${
          isActive
            ? 'bg-purple-100 text-purple-700 border border-purple-200'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
        title={expanded ? undefined : typeof item.label === 'string' ? item.label : item.description}
      >
        <div className="flex items-center space-x-3">
          <item.icon className={`h-5 w-5 ${isActive ? 'text-purple-600' : 'text-gray-500'}`} />
          {expanded && (
            <span className="font-medium">
              {typeof item.label === 'string' ? item.label : item.label}
            </span>
          )}
        </div>
        
        {expanded && hasChildren && (
          <div className="flex items-center space-x-2">
            {item.children && (
              <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                {item.children.length}
              </span>
            )}
            {isOpen ? (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </div>
        )}
      </button>

      {/* Child Items */}
      {expanded && hasChildren && isOpen && (
        <div className="mt-1 space-y-1">
          {item.children?.map((child) => (
            <NavigationItemComponent
              key={child.id}
              item={child}
              currentView={currentView}
              onItemClick={onItemClick}
              expanded={expanded}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default NavigationCentralized;
