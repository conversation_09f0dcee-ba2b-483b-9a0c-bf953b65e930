
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --header-height: 7rem; /* 112px - increased to ensure proper clearance with subtitle */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 258 47% 29%; /* iLEAD purple (was green) */
    --primary-foreground: 210 40% 98%;

    --secondary: 27 81% 52%; /* iLEAD orange */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 258 47% 29%; /* iLEAD purple (was green) */

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 258 47% 29%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 27 81% 52%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 258 47% 29%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 258 47% 29%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 27 81% 52%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 258 47% 29%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 258 47% 29%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 27 81% 52%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 258 47% 29%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar Styles */
@layer utilities {
  /* Webkit browsers (Chrome, Safari, Edge) */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 4px;
    border: 1px solid rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  /* Force scrollbar to always be visible */
  .scrollbar-thin::-webkit-scrollbar-thumb {
    min-height: 20px;
  }

  /* Alternative scrollbar that's always visible */
  .scrollbar-visible {
    scrollbar-width: auto;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
    overflow-y: scroll !important;
  }

  .scrollbar-visible::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    background: rgb(243 244 246);
  }

  .scrollbar-visible::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 6px;
    margin: 2px;
  }

  .scrollbar-visible::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 6px;
    min-height: 40px;
    border: 2px solid rgb(243 244 246);
  }

  .scrollbar-visible::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  .scrollbar-visible::-webkit-scrollbar-corner {
    background: rgb(243 244 246);
  }

  /* Custom scrollbar for main content areas */
  .scrollbar-content {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(249 250 251);
  }

  .scrollbar-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-content::-webkit-scrollbar-track {
    background: rgb(249 250 251);
    border-radius: 4px;
  }

  .scrollbar-content::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 4px;
  }

  .scrollbar-content::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Standardized page spacing utilities */
  .page-container {
    @apply p-4 sm:p-6 lg:p-8 space-y-6;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Enhanced mobile-friendly button sizes */
  .btn-mobile {
    @apply min-h-[44px] min-w-[44px] touch-manipulation text-base;
  }

  .btn-mobile-large {
    @apply min-h-[48px] min-w-[48px] touch-manipulation text-base px-6;
  }

  .btn-mobile-small {
    @apply min-h-[40px] min-w-[40px] touch-manipulation text-sm px-3;
  }

  /* Enhanced mobile-friendly form inputs */
  .input-mobile {
    @apply min-h-[44px] text-base px-4 py-3;
  }

  .input-mobile-large {
    @apply min-h-[48px] text-base px-4 py-3;
  }

  .textarea-mobile {
    @apply min-h-[88px] text-base px-4 py-3 resize-none;
  }

  .select-mobile {
    @apply min-h-[44px] text-base;
  }

  /* Mobile table improvements */
  .table-mobile-scroll {
    @apply overflow-x-auto scrollbar-thin;
  }

  .table-mobile-stack {
    @apply block sm:table;
  }

  .table-mobile-stack thead,
  .table-mobile-stack tbody,
  .table-mobile-stack th,
  .table-mobile-stack td,
  .table-mobile-stack tr {
    @apply block sm:table-row sm:table-cell sm:table-header-group sm:table-row-group;
  }

  .table-mobile-stack thead tr {
    @apply absolute -top-full -left-full sm:relative sm:top-auto sm:left-auto;
  }

  .table-mobile-stack tr {
    @apply border border-gray-200 mb-2 sm:border-none sm:mb-0;
  }

  .table-mobile-stack td {
    @apply border-none relative pl-12 sm:pl-0 py-2 sm:py-1;
  }

  .table-mobile-stack td:before {
    content: attr(data-label) ": ";
    @apply absolute left-2 top-2 font-medium text-gray-600 sm:hidden;
  }

  /* Enhanced mobile card spacing */
  .card-mobile {
    @apply p-4 space-y-3;
  }

  .card-mobile-compact {
    @apply p-3 space-y-2;
  }

  .card-mobile-spacious {
    @apply p-6 space-y-4;
  }

  /* Mobile navigation patterns */
  .nav-mobile-bottom {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 sm:hidden;
  }

  .nav-mobile-tab {
    @apply flex-1 flex flex-col items-center justify-center py-2 px-1 text-xs touch-manipulation;
  }

  /* Mobile modal patterns */
  .modal-mobile-fullscreen {
    @apply fixed inset-0 z-50 bg-white sm:relative sm:bg-transparent;
  }

  .modal-mobile-bottom-sheet {
    @apply fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-lg border-t border-gray-200 sm:relative sm:bg-transparent sm:border-none sm:rounded-none;
  }

  /* Mobile spacing utilities */
  .spacing-mobile-tight {
    @apply space-y-2 sm:space-y-3;
  }

  .spacing-mobile-normal {
    @apply space-y-3 sm:space-y-4;
  }

  .spacing-mobile-loose {
    @apply space-y-4 sm:space-y-6;
  }

  /* Mobile grid patterns */
  .grid-mobile-stack {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .grid-mobile-auto {
    @apply grid grid-cols-1 sm:grid-cols-auto gap-4;
  }

  /* Mobile text patterns */
  .text-mobile-responsive {
    @apply text-sm sm:text-base;
  }

  .text-mobile-heading {
    @apply text-lg sm:text-xl lg:text-2xl font-semibold;
  }

  .text-mobile-subheading {
    @apply text-base sm:text-lg font-medium;
  }

  /* Mobile interaction patterns */
  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }

  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-first responsive containers */
  .container-mobile {
    @apply w-full max-w-none px-4 sm:max-w-screen-sm sm:px-6 lg:max-w-screen-lg lg:px-8 xl:max-w-screen-xl;
  }

  .container-mobile-full {
    @apply w-full px-4 sm:px-6 lg:px-8;
  }

  .page-header {
    @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  }

  .page-title {
    @apply text-2xl sm:text-3xl font-bold text-gray-900 leading-tight;
  }

  .page-description {
    @apply text-gray-600 mt-1 text-sm sm:text-base;
  }

  .content-section {
    @apply space-y-4;
  }

  .action-buttons {
    @apply flex items-center gap-2 flex-wrap;
  }
}
