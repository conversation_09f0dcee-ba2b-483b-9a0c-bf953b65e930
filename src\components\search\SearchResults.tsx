import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  School, 
  CheckSquare, 
  FileText, 
  BookOpen, 
  User, 
  Package,
  Search,
  Loader2,
  ExternalLink
} from 'lucide-react';
import { SearchResult } from '@/hooks/useRoleAwareSearch';

interface SearchResultsProps {
  results: SearchResult[];
  isLoading: boolean;
  query: string;
  onResultClick: (result: SearchResult) => void;
  onClose?: () => void;
}

const getResultIcon = (type: SearchResult['type']) => {
  switch (type) {
    case 'school':
      return School;
    case 'task':
      return CheckSquare;
    case 'field_report':
      return FileText;
    case 'book':
      return BookOpen;
    case 'user':
      return User;
    case 'distribution':
      return Package;
    default:
      return Search;
  }
};

const getResultTypeLabel = (type: SearchResult['type']) => {
  switch (type) {
    case 'school':
      return 'School';
    case 'task':
      return 'Task';
    case 'field_report':
      return 'Field Report';
    case 'book':
      return 'Book';
    case 'user':
      return 'Staff';
    case 'distribution':
      return 'Distribution';
    default:
      return 'Result';
  }
};

const getResultTypeColor = (type: SearchResult['type']) => {
  switch (type) {
    case 'school':
      return 'bg-blue-100 text-blue-800';
    case 'task':
      return 'bg-green-100 text-green-800';
    case 'field_report':
      return 'bg-purple-100 text-purple-800';
    case 'book':
      return 'bg-orange-100 text-orange-800';
    case 'user':
      return 'bg-gray-100 text-gray-800';
    case 'distribution':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  isLoading,
  query,
  onResultClick,
  onClose
}) => {
  if (!query.trim() || query.length < 2) {
    return (
      <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg">
        <CardContent className="p-4">
          <div className="text-center text-gray-500">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Type at least 2 characters to search</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg">
        <CardContent className="p-4">
          <div className="text-center text-gray-500">
            <Loader2 className="h-6 w-6 mx-auto mb-2 animate-spin" />
            <p className="text-sm">Searching...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (results.length === 0) {
    return (
      <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg">
        <CardContent className="p-4">
          <div className="text-center text-gray-500">
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No results found for "{query}"</p>
            <p className="text-xs text-gray-400 mt-1">
              Try different keywords or check your spelling
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg max-h-96 overflow-y-auto">
      <CardContent className="p-0">
        {/* Header */}
        <div className="p-3 border-b bg-gray-50 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">
              {results.length} result{results.length !== 1 ? 's' : ''} for "{query}"
            </span>
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>

        {/* Results */}
        <div className="max-h-80 overflow-y-auto">
          {results.map((result, index) => {
            const Icon = getResultIcon(result.type);
            const typeLabel = getResultTypeLabel(result.type);
            const typeColor = getResultTypeColor(result.type);

            return (
              <div
                key={`${result.type}-${result.id}-${index}`}
                className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 transition-colors"
                onClick={() => onResultClick(result)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    <Icon className="h-4 w-4 text-gray-500" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {result.title}
                      </h4>
                      <Badge variant="secondary" className={`text-xs ${typeColor}`}>
                        {typeLabel}
                      </Badge>
                    </div>
                    
                    {result.description && (
                      <p className="text-xs text-gray-600 line-clamp-2">
                        {result.description}
                      </p>
                    )}
                    
                    {/* Metadata */}
                    {result.metadata && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {Object.entries(result.metadata).slice(0, 3).map(([key, value]) => (
                          <Badge 
                            key={key} 
                            variant="outline" 
                            className="text-xs px-1 py-0"
                          >
                            {String(value)}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-shrink-0">
                    <ExternalLink className="h-3 w-3 text-gray-400" />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        {results.length >= 20 && (
          <div className="p-3 border-t bg-gray-50 text-center">
            <p className="text-xs text-gray-500">
              Showing first {results.length} results. Try more specific keywords for better results.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SearchResults;
