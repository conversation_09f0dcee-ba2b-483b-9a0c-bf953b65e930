-- Fix get_user_activities function type mismatch
-- The function needs to match the expected return types exactly

-- Drop the existing function first
DROP FUNCTION IF EXISTS get_user_activities(UUID, INTEGER);

-- Recreate the function with correct return types
CREATE OR REPLACE FUNCTION get_user_activities(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    activity_type activity_type,
    user_id UUID,
    user_name TEXT,
    entity_type entity_type,
    entity_id UUID,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    entity_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to view activities for this user
    IF p_user_id != auth.uid() AND NOT EXISTS (
        SELECT 1 FROM profiles p_check
        WHERE p_check.id = auth.uid() AND p_check.role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to view user activities';
    END IF;

    RETURN QUERY
    SELECT 
        a.id,
        a.activity_type,
        a.user_id,
        p.name::TEXT as user_name,  -- Explicit cast to TEXT
        a.entity_type,
        a.entity_id,
        a.description,
        COALESCE(a.metadata, '{}'::jsonb) as metadata,  -- Ensure not null
        a.created_at,
        CASE 
            WHEN a.entity_type = 'task' THEN
                COALESCE((SELECT jsonb_build_object(
                    'title', t.title,
                    'status', t.status,
                    'priority', t.priority
                ) FROM tasks t WHERE t.id = a.entity_id), '{}'::jsonb)
            WHEN a.entity_type = 'school' THEN
                COALESCE((SELECT jsonb_build_object(
                    'name', s.name,
                    'school_type', s.school_type
                ) FROM schools s WHERE s.id = a.entity_id), '{}'::jsonb)
            WHEN a.entity_type = 'distribution' THEN
                COALESCE((SELECT jsonb_build_object(
                    'school_name', s.name,
                    'quantity', bd.quantity
                ) FROM book_distributions bd
                JOIN schools s ON bd.school_id = s.id
                WHERE bd.id = a.entity_id), '{}'::jsonb)
            ELSE '{}'::jsonb
        END as entity_details
    FROM activities a
    JOIN profiles p ON a.user_id = p.id
    WHERE a.user_id = p_user_id
    ORDER BY a.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_activities(UUID, INTEGER) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_user_activities(UUID, INTEGER) IS 'Get activities for a specific user with proper type casting';
