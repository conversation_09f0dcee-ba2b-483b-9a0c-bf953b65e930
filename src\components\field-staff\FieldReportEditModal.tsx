import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  X, 
  Loader2,
  AlertCircle,
  Users,
  Target,
  BookOpen
} from 'lucide-react';
import { useFieldReport, useUpdateFieldReport } from '@/hooks/field-staff/useFieldReports';
import { useToast } from '@/hooks/use-toast';
import { 
  EDUCATION_LEVELS,
  FieldReportFormData,
  validateFieldReport
} from '@/utils/fieldReportValidation';
import { 
  ALL_LESSON_TOPICS,
  getLessonsByCategory,
  LESSON_CATEGORIES
} from '@/constants/lessonTopics';

interface FieldReportEditModalProps {
  reportId: string;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const FieldReportEditModal: React.FC<FieldReportEditModalProps> = ({
  reportId,
  isOpen,
  onClose,
  onSave
}) => {
  const { toast } = useToast();
  const { data: report, isLoading: loadingReport } = useFieldReport(reportId);
  const updateReportMutation = useUpdateFieldReport();

  const [formData, setFormData] = useState<Partial<FieldReportFormData>>({
    activity_type: 'round_table_session',
    round_table_sessions_count: 0,
    total_students_attended: 0,
    students_per_session: 8,
    activities_conducted: [],
    topics_covered: [],
    challenges_encountered: '',
    wins_achieved: '',
    general_observations: '',
    lessons_learned: '',
    follow_up_required: false,
    follow_up_actions: '',
    introduction: '',
    recommendations: '',
  });

  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load report data into form when report is fetched
  useEffect(() => {
    if (report) {
      setFormData({
        activity_type: report.activity_type,
        round_table_sessions_count: report.round_table_sessions_count || 0,
        total_students_attended: report.total_students_attended || 0,
        students_per_session: report.students_per_session || 8,
        activities_conducted: report.activities_conducted || [],
        topics_covered: report.topics_covered || [],
        challenges_encountered: report.challenges_encountered || '',
        wins_achieved: report.wins_achieved || '',
        general_observations: report.general_observations || '',
        lessons_learned: report.lessons_learned || '',
        follow_up_required: report.follow_up_required || false,
        follow_up_actions: report.follow_up_actions || '',
        introduction: report.introduction || '',
        recommendations: report.recommendations || '',
      });
    }
  }, [report]);

  const handleInputChange = (field: keyof FieldReportFormData, value: unknown) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleArrayInputChange = (field: 'activities_conducted' | 'topics_covered', value: string) => {
    const currentArray = formData[field] || [];
    if (value && !currentArray.includes(value)) {
      handleInputChange(field, [...currentArray, value]);
    }
  };

  const removeArrayItem = (field: 'activities_conducted' | 'topics_covered', index: number) => {
    const currentArray = formData[field] || [];
    const newArray = currentArray.filter((_, i) => i !== index);
    handleInputChange(field, newArray);
  };

  const handleSave = async () => {
    try {
      // Validate form data
      const validation = validateFieldReport(formData as FieldReportFormData);
      if (!validation.isValid) {
        setErrors(validation.fieldErrors);
        toast({
          title: "Validation Error",
          description: "Please fix the errors in the form before saving.",
          variant: "destructive",
        });
        return;
      }

      // Update the report
      await updateReportMutation.mutateAsync({
        reportId,
        data: formData as FieldReportFormData
      });

      toast({
        title: "Report Updated",
        description: "The field report has been successfully updated.",
      });

      onSave();
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update the field report. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatActivityType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const availableLessons = selectedCategory ? getLessonsByCategory(selectedCategory) : [];

  if (loadingReport) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading report...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!report) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-8 text-red-600">
            <AlertCircle className="h-8 w-8 mr-2" />
            <span>Failed to load report</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Edit Field Report - {report.school_name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="activity_type">Activity Type</Label>
              <Select 
                value={formData.activity_type} 
                onValueChange={(value) => handleInputChange('activity_type', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="round_table_session">Round Table Session</SelectItem>
                  <SelectItem value="school_visit">School Visit</SelectItem>
                  <SelectItem value="meeting">Meeting</SelectItem>
                  <SelectItem value="assessment">Assessment</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              {errors.activity_type && (
                <p className="text-sm text-red-600">{errors.activity_type}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="round_table_sessions">Number of Sessions</Label>
              <Input
                id="round_table_sessions"
                type="number"
                min="0"
                value={formData.round_table_sessions || 0}
                onChange={(e) => handleInputChange('round_table_sessions', parseInt(e.target.value) || 0)}
              />
              {errors.round_table_sessions && (
                <p className="text-sm text-red-600">{errors.round_table_sessions}</p>
              )}
            </div>
          </div>

          {/* Student Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="total_students_attended">Total Students Present</Label>
              <Input
                id="total_students_attended"
                type="number"
                min="0"
                value={formData.total_students_attended || 0}
                onChange={(e) => handleInputChange('total_students_attended', parseInt(e.target.value) || 0)}
              />
              {errors.total_students_attended && (
                <p className="text-sm text-red-600">{errors.total_students_attended}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="students_per_session">Students per Session</Label>
              <Input
                id="students_per_session"
                type="number"
                min="1"
                value={formData.students_per_session || 8}
                onChange={(e) => handleInputChange('students_per_session', parseInt(e.target.value) || 8)}
              />
            </div>
          </div>

          {/* Introduction */}
          <div className="space-y-2">
            <Label htmlFor="introduction">Introduction</Label>
            <Textarea
              id="introduction"
              placeholder="Brief introduction to the activity..."
              value={formData.introduction || ''}
              onChange={(e) => handleInputChange('introduction', e.target.value)}
              rows={3}
            />
          </div>

          {/* Topics Covered */}
          <div className="space-y-4">
            <Label>Lesson/Topic Covered</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {LESSON_CATEGORIES.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Lesson</Label>
                <Select 
                  value="" 
                  onValueChange={(value) => handleArrayInputChange('topics_covered', value)}
                  disabled={!selectedCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select lesson" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableLessons.map(lesson => (
                      <SelectItem key={lesson.id} value={lesson.id}>
                        {lesson.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Selected Topics */}
            {formData.topics_covered && formData.topics_covered.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Topics</Label>
                <div className="flex flex-wrap gap-2">
                  {formData.topics_covered.map((topic, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {topic}
                      <button
                        type="button"
                        onClick={() => removeArrayItem('topics_covered', index)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Challenges and Wins */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="challenges_encountered">Challenges Encountered</Label>
              <Textarea
                id="challenges_encountered"
                placeholder="Describe any challenges faced..."
                value={formData.challenges_encountered || ''}
                onChange={(e) => handleInputChange('challenges_encountered', e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="wins_achieved">Wins Achieved</Label>
              <Textarea
                id="wins_achieved"
                placeholder="Describe successes and achievements..."
                value={formData.wins_achieved || ''}
                onChange={(e) => handleInputChange('wins_achieved', e.target.value)}
                rows={4}
              />
            </div>
          </div>

          {/* Follow-up Actions */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="follow_up_required"
                checked={formData.follow_up_required || false}
                onCheckedChange={(checked) => handleInputChange('follow_up_required', checked)}
              />
              <Label htmlFor="follow_up_required">Follow-up actions required</Label>
            </div>

            {formData.follow_up_required && (
              <div className="space-y-2">
                <Label htmlFor="follow_up_actions">Follow-up Actions</Label>
                <Textarea
                  id="follow_up_actions"
                  placeholder="Describe the follow-up actions needed..."
                  value={formData.follow_up_actions || ''}
                  onChange={(e) => handleInputChange('follow_up_actions', e.target.value)}
                  rows={3}
                />
              </div>
            )}
          </div>

          {/* Recommendations */}
          <div className="space-y-2">
            <Label htmlFor="recommendations">Recommendations</Label>
            <Textarea
              id="recommendations"
              placeholder="Any recommendations for future activities..."
              value={formData.recommendations || ''}
              onChange={(e) => handleInputChange('recommendations', e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={updateReportMutation.isPending}
          >
            {updateReportMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FieldReportEditModal;
