import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { ConflictData, CONFLICT_RESOLUTION } from '@/types/offlineSync.types';
import { 
  Alert<PERSON>riangle, 
  Clock, 
  Database, 
  Smartphone, 
  Shield, 
  Info,
  CheckCircle,
  XCircle,
  Merge,
  Eye,
  Save
} from 'lucide-react';

interface ConflictResolutionDialogProps {
  conflict: ConflictData | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onResolve: (conflictId: string, strategy: keyof typeof CONFLICT_RESOLUTION, customData?: Record<string, unknown>) => void;
}

const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  conflict,
  open,
  onOpenChange,
  onResolve,
}) => {
  const [selectedStrategy, setSelectedStrategy] = useState<keyof typeof CONFLICT_RESOLUTION | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [customResolution, setCustomResolution] = useState<Record<string, unknown>>({});
  const { toast } = useToast();

  if (!conflict) return null;

  const formatValue = (value: unknown): string => {
    if (value === null || value === undefined) return 'Not set';
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'string' && value.length === 0) return 'Empty';
    return String(value);
  };

  const getFieldDisplayName = (fieldName: string): string => {
    const fieldMap: Record<string, string> = {
      'school_id': 'School',
      'latitude': 'Latitude',
      'longitude': 'Longitude',
      'accuracy': 'GPS Accuracy',
      'activity_type': 'Activity Type',
      'participants_male': 'Male Participants',
      'participants_female': 'Female Participants',
      'notes': 'Notes',
      'lesson_topic': 'Lesson Topic',
      'venue': 'Venue',
      'duration_minutes': 'Duration (minutes)',
    };
    return fieldMap[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getStrategyInfo = (strategy: keyof typeof CONFLICT_RESOLUTION) => {
    switch (strategy) {
      case 'CLIENT_WINS':
        return {
          title: 'Use Your Changes',
          description: 'Keep your local changes and overwrite the server data',
          icon: Smartphone,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          risk: 'low',
          riskText: 'Your changes will be preserved, but server changes will be lost'
        };
      case 'SERVER_WINS':
        return {
          title: 'Use Server Version',
          description: 'Accept the server data and discard your local changes',
          icon: Database,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          risk: 'medium',
          riskText: 'Your local changes will be permanently lost'
        };
      case 'MERGE':
        return {
          title: 'Smart Merge',
          description: 'Automatically combine both versions using intelligent merging',
          icon: Merge,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          risk: 'low',
          riskText: 'Both versions will be combined, but review the result carefully'
        };
      case 'MANUAL':
        return {
          title: 'Custom Resolution',
          description: 'Manually choose which fields to keep from each version',
          icon: Eye,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          risk: 'low',
          riskText: 'You have full control over the final result'
        };
    }
  };

  const handleResolve = () => {
    if (!selectedStrategy) return;

    const strategyInfo = getStrategyInfo(selectedStrategy);
    if (strategyInfo.risk === 'medium') {
      setShowConfirmation(true);
    } else {
      executeResolution();
    }
  };

  const executeResolution = () => {
    if (!selectedStrategy) return;

    onResolve(conflict.id, selectedStrategy, selectedStrategy === 'MANUAL' ? customResolution : undefined);
    setShowConfirmation(false);
    onOpenChange(false);
    setSelectedStrategy(null);
    setCustomResolution({});
    
    toast({
      title: "Conflict Resolved",
      description: `Successfully resolved using ${getStrategyInfo(selectedStrategy).title}`,
      variant: "default",
    });
  };

  const getConflictAge = () => {
    const ageMs = Date.now() - conflict.timestamp;
    const ageMinutes = Math.floor(ageMs / (1000 * 60));
    const ageHours = Math.floor(ageMinutes / 60);
    const ageDays = Math.floor(ageHours / 24);

    if (ageDays > 0) return `${ageDays} day${ageDays > 1 ? 's' : ''} ago`;
    if (ageHours > 0) return `${ageHours} hour${ageHours > 1 ? 's' : ''} ago`;
    if (ageMinutes > 0) return `${ageMinutes} minute${ageMinutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Data Conflict Resolution
            </DialogTitle>
            <DialogDescription>
              A conflict was detected between your local changes and the server data. 
              Please review the differences and choose how to resolve this conflict.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Conflict Info */}
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Conflict Information
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Conflict ID:</span> {conflict.id.slice(0, 8)}...
                  </div>
                  <div>
                    <span className="font-medium">Detected:</span> {getConflictAge()}
                  </div>
                  <div>
                    <span className="font-medium">Type:</span> {conflict.type}
                  </div>
                  <div>
                    <span className="font-medium">Conflicting Fields:</span> {conflict.conflictFields.length}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Data Comparison */}
            <Tabs defaultValue="comparison" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="comparison">Compare Data</TabsTrigger>
                <TabsTrigger value="resolution">Choose Resolution</TabsTrigger>
              </TabsList>

              <TabsContent value="comparison" className="space-y-4">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Local Data */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Smartphone className="h-4 w-4 text-blue-600" />
                        Your Local Changes
                      </CardTitle>
                      <CardDescription>
                        Data stored on your device
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {conflict.conflictFields.map((field) => (
                        <div key={field} className="space-y-1">
                          <div className="font-medium text-sm">{getFieldDisplayName(field)}</div>
                          <div className="p-2 bg-blue-50 border border-blue-200 rounded text-sm">
                            {formatValue(conflict.localData[field])}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  {/* Server Data */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Database className="h-4 w-4 text-green-600" />
                        Server Version
                      </CardTitle>
                      <CardDescription>
                        Data currently on the server
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {conflict.conflictFields.map((field) => (
                        <div key={field} className="space-y-1">
                          <div className="font-medium text-sm">{getFieldDisplayName(field)}</div>
                          <div className="p-2 bg-green-50 border border-green-200 rounded text-sm">
                            {formatValue(conflict.serverData[field])}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="resolution" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {(Object.keys(CONFLICT_RESOLUTION) as Array<keyof typeof CONFLICT_RESOLUTION>).map((strategy) => {
                    const info = getStrategyInfo(strategy);
                    const Icon = info.icon;
                    const isSelected = selectedStrategy === strategy;

                    return (
                      <Card 
                        key={strategy}
                        className={`cursor-pointer transition-all ${
                          isSelected 
                            ? `${info.borderColor} ${info.bgColor} ring-2 ring-offset-2 ring-blue-500` 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedStrategy(strategy)}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className={`text-sm flex items-center gap-2 ${info.color}`}>
                            <Icon className="h-4 w-4" />
                            {info.title}
                            {isSelected && <CheckCircle className="h-4 w-4 text-blue-600 ml-auto" />}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <p className="text-sm text-gray-600 mb-3">{info.description}</p>
                          <div className="flex items-center gap-2">
                            <Badge variant={info.risk === 'medium' ? 'destructive' : 'secondary'}>
                              {info.risk === 'medium' ? 'Data Loss Risk' : 'Safe'}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-500 mt-2">{info.riskText}</p>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>

                {selectedStrategy && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="pt-6">
                      <div className="flex items-start gap-3">
                        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-blue-900 mb-1">Resolution Preview</h4>
                          <p className="text-sm text-blue-700">
                            {getStrategyInfo(selectedStrategy).riskText}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleResolve}
              disabled={!selectedStrategy}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              Resolve Conflict
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog for Risky Operations */}
      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Confirm Data Loss
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete your local changes. This cannot be undone.
              Are you sure you want to proceed with using the server version?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={executeResolution}
              className="bg-red-600 hover:bg-red-700"
            >
              Yes, Delete My Changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ConflictResolutionDialog;
