import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { BookOpen, AlertCircle, AlertTriangle, Loader2, ChevronDown, ChevronUp, Package, DollarSign } from 'lucide-react';
import { useBookOperations } from '@/hooks/useBookOperations';
import { BookCondition, BookLanguage } from '@/types/book';
import { useFormValidation } from '@/hooks/useFormValidation';
import { validationSchemas } from '@/utils/validation';
import { toast } from 'sonner';

interface AddBookModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// BookFormData is now imported from validation utils

const AddBookModal: React.FC<AddBookModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [formData, setFormData] = useState<BookFormData>({
    title: '',
    language: 'english',
    description: '',
    total_quantity: '',
    condition: 'good',
    minimum_threshold: '10',
    notes: '',
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [serverWarnings, setServerWarnings] = useState<string[]>([]);
  const [showQuickTips, setShowQuickTips] = useState(false);

  const { addBook, isAddingBook } = useBookOperations();

  // Use the enhanced validation from utils
  const validateForm = () => {
    return validateBookForm(formData);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validation = validateForm();
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      setFieldErrors(validation.fieldErrors);
      return;
    }

    // Server-side validation for duplicates
    const serverValidation = await validateBookServerSide({
      title: formData.title.trim(),
      author: 'iLead Program', // Default author for all iLead books
      isbn: undefined, // No longer collected
    });

    if (!serverValidation.isValid) {
      setValidationErrors(serverValidation.errors);
      return;
    }

    if (serverValidation.warnings.length > 0) {
      setServerWarnings(serverValidation.warnings);
    }

    try {
      const bookData = {
        title: formData.title,
        language: formData.language,
        description: formData.description.trim() || undefined,
        total_quantity: formData.total_quantity ? parseInt(formData.total_quantity) : 0,
        condition: formData.condition,
        minimum_threshold: formData.minimum_threshold ? parseInt(formData.minimum_threshold) : 10,
        notes: formData.notes.trim() || undefined,
      };

      await addBook(bookData);
      
      // Reset form and close modal
      setFormData({
        title: '',
        language: 'english',
        description: '',
        total_quantity: '',
        condition: 'good',
        minimum_threshold: '10',
        notes: '',
      });
      setValidationErrors([]);
      setFieldErrors({});
      setServerWarnings([]);
      onClose();
      toast.success('Book added successfully!');
    } catch (error) {
      console.error('Failed to add book:', error);
      toast.error('Failed to add book. Please try again.');
    }
  };

  const handleClose = () => {
    setValidationErrors([]);
    setFieldErrors({});
    onClose();
  };

  const updateField = (field: keyof BookFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Real-time field validation
    const fieldValidation = validateField(field, value, formData);
    if (!fieldValidation.isValid && fieldValidation.error) {
      setFieldErrors(prev => ({ ...prev, [field]: fieldValidation.error! }));
    } else {
      setFieldErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Clear general validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5 text-blue-600" />
            Add New Book
          </DialogTitle>
          <DialogDescription>
            Add a new book to the inventory with complete information and stock details.
          </DialogDescription>
        </DialogHeader>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Server Warnings */}
        {serverWarnings.length > 0 && (
          <Alert className="border-yellow-200 bg-yellow-50">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-yellow-800">
              <ul className="list-disc list-inside space-y-1">
                {serverWarnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Book Title *</Label>
                  <Select value={formData.title} onValueChange={(value) => updateField('title', value)}>
                    <SelectTrigger className={fieldErrors.title ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select a book title" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="iLead">iLead</SelectItem>
                      <SelectItem value="iDo">iDo</SelectItem>
                      <SelectItem value="iChoose">iChoose</SelectItem>
                    </SelectContent>
                  </Select>
                  {fieldErrors.title && (
                    <p className="text-sm text-red-600">{fieldErrors.title}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="language">Language *</Label>
                  <Select value={formData.language} onValueChange={(value: BookLanguage) => updateField('language', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="english">English</SelectItem>
                      <SelectItem value="luganda">Luganda</SelectItem>
                      <SelectItem value="runyankole">Runyankole</SelectItem>
                      <SelectItem value="ateso">Ateso</SelectItem>
                      <SelectItem value="luo">Luo</SelectItem>
                      <SelectItem value="lugbara">Lugbara</SelectItem>
                      <SelectItem value="runyoro">Runyoro</SelectItem>
                      <SelectItem value="lusoga">Lusoga</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description/Notes</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateField('description', e.target.value)}
                  placeholder="Brief description of the book content, target audience, or special features"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>



          {/* Inventory Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Package className="h-4 w-4" />
                Inventory Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="total_quantity">Total Quantity Available</Label>
                  <Input
                    id="total_quantity"
                    type="number"
                    value={formData.total_quantity}
                    onChange={(e) => updateField('total_quantity', e.target.value)}
                    placeholder="e.g., 100"
                    min="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minimum_threshold">Minimum Stock Threshold</Label>
                  <Input
                    id="minimum_threshold"
                    type="number"
                    value={formData.minimum_threshold}
                    onChange={(e) => updateField('minimum_threshold', e.target.value)}
                    placeholder="e.g., 10"
                    min="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="condition">Book Condition</Label>
                  <Select value={formData.condition} onValueChange={(value: BookCondition) => updateField('condition', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">New</SelectItem>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="fair">Fair</SelectItem>
                      <SelectItem value="poor">Poor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => updateField('notes', e.target.value)}
                  placeholder="Any additional notes about the inventory, supplier information, or special handling instructions"
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>



          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isAddingBook}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isAddingBook}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isAddingBook ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Adding Book...
                </>
              ) : (
                <>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Add Book
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddBookModal;
