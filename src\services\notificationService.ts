import { supabase } from '@/integrations/supabase/client';
import { NotificationType, NotificationPriority, CreateNotificationData } from '@/hooks/useNotifications';

export interface NotificationRecipient {
  id: string;
  role: string;
  name?: string;
}

export interface NotificationContext {
  entityType?: string;
  entityId?: string;
  metadata?: Record<string, any>;
  actionUrl?: string;
}

export class NotificationService {
  // Get users by role for role-based notifications
  static async getUsersByRole(roles: string[]): Promise<NotificationRecipient[]> {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, role, name')
      .in('role', roles);

    if (error) {
      console.error('Error fetching users by role:', error);
      return [];
    }

    return data.map(user => ({
      id: user.id,
      role: user.role,
      name: user.name
    }));
  }

  // Get users assigned to specific schools (for field staff)
  static async getFieldStaffBySchool(schoolId: string): Promise<NotificationRecipient[]> {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, role, name')
      .eq('role', 'field_staff')
      .contains('assigned_schools', [schoolId]);

    if (error) {
      console.error('Error fetching field staff by school:', error);
      return [];
    }

    return data.map(user => ({
      id: user.id,
      role: user.role,
      name: user.name
    }));
  }

  // Create notification for specific users
  static async createNotification(
    type: NotificationType,
    title: string,
    message: string,
    recipients: NotificationRecipient[],
    options?: {
      priority?: NotificationPriority;
      senderId?: string;
      senderRole?: string;
      context?: NotificationContext;
      expiresAt?: Date;
    }
  ): Promise<void> {
    const notifications = recipients.map(recipient => ({
      type,
      title,
      message,
      recipient_id: recipient.id,
      recipient_role: recipient.role,
      priority: options?.priority || 'normal',
      sender_id: options?.senderId,
      sender_role: options?.senderRole,
      related_entity_type: options?.context?.entityType,
      related_entity_id: options?.context?.entityId,
      metadata: options?.context?.metadata || {},
      action_url: options?.context?.actionUrl,
      expires_at: options?.expiresAt?.toISOString(),
    }));

    const { error } = await supabase
      .from('notifications')
      .insert(notifications);

    if (error) {
      console.error('Error creating notifications:', error);
      throw error;
    }
  }

  // Role-aware notification methods

  // Notify when a task is assigned
  static async notifyTaskAssigned(
    taskId: string,
    taskTitle: string,
    assignedToId: string,
    assignedByName: string,
    assignedById?: string,
    assignedByRole?: string
  ): Promise<void> {
    const { data: assignee } = await supabase
      .from('profiles')
      .select('id, role, name')
      .eq('id', assignedToId)
      .single();

    if (!assignee) return;

    await this.createNotification(
      'task_assigned',
      'New Task Assigned',
      `You have been assigned a new task: "${taskTitle}" by ${assignedByName}`,
      [{ id: assignee.id, role: assignee.role, name: assignee.name }],
      {
        priority: 'normal',
        senderId: assignedById,
        senderRole: assignedByRole,
        context: {
          entityType: 'task',
          entityId: taskId,
          actionUrl: 'tasks',
          metadata: { task_title: taskTitle, assigned_by: assignedByName }
        }
      }
    );
  }

  // Notify when a field report is submitted
  static async notifyFieldReportSubmitted(
    reportId: string,
    schoolName: string,
    staffName: string,
    staffId: string
  ): Promise<void> {
    // Notify program officers and admins
    const recipients = await this.getUsersByRole(['admin', 'program_officer']);

    await this.createNotification(
      'field_report_submitted',
      'New Field Report Submitted',
      `A new field report has been submitted by ${staffName} for ${schoolName}`,
      recipients,
      {
        priority: 'normal',
        senderId: staffId,
        senderRole: 'field_staff',
        context: {
          entityType: 'field_report',
          entityId: reportId,
          actionUrl: 'staff-reports',
          metadata: { school_name: schoolName, staff_name: staffName }
        }
      }
    );
  }

  // Notify when a field report is approved/rejected
  static async notifyFieldReportStatus(
    reportId: string,
    schoolName: string,
    staffId: string,
    approved: boolean,
    reason?: string,
    reviewerId?: string,
    reviewerRole?: string
  ): Promise<void> {
    const { data: staff } = await supabase
      .from('profiles')
      .select('id, role, name')
      .eq('id', staffId)
      .single();

    if (!staff) return;

    const type = approved ? 'field_report_approved' : 'field_report_rejected';
    const title = approved ? 'Field Report Approved' : 'Field Report Needs Revision';
    const message = approved 
      ? `Your field report for ${schoolName} has been approved`
      : `Your field report for ${schoolName} needs revision. ${reason ? `Reason: ${reason}` : ''}`;

    await this.createNotification(
      type,
      title,
      message,
      [{ id: staff.id, role: staff.role, name: staff.name }],
      {
        priority: approved ? 'low' : 'high',
        senderId: reviewerId,
        senderRole: reviewerRole,
        context: {
          entityType: 'field_report',
          entityId: reportId,
          actionUrl: 'field-visits',
          metadata: { school_name: schoolName, approved, reason }
        }
      }
    );
  }

  // Notify when a new school is added
  static async notifySchoolAdded(
    schoolId: string,
    schoolName: string,
    district: string,
    addedById?: string,
    addedByRole?: string
  ): Promise<void> {
    // Notify all users except field staff (they only need to know about their assigned schools)
    const recipients = await this.getUsersByRole(['admin', 'program_officer']);

    await this.createNotification(
      'school_added',
      'New School Added',
      `A new school has been added: ${schoolName} in ${district}`,
      recipients,
      {
        priority: 'low',
        senderId: addedById,
        senderRole: addedByRole,
        context: {
          entityType: 'school',
          entityId: schoolId,
          actionUrl: 'schools',
          metadata: { school_name: schoolName, district }
        }
      }
    );
  }

  // Notify when a new user is created
  static async notifyUserCreated(
    userId: string,
    userName: string,
    userRole: string,
    createdById?: string,
    createdByRole?: string
  ): Promise<void> {
    // Notify admins and program officers about new users
    const recipients = await this.getUsersByRole(['admin', 'program_officer']);

    await this.createNotification(
      'user_created',
      'New User Added',
      `A new ${userRole} user has been added: ${userName}`,
      recipients,
      {
        priority: 'low',
        senderId: createdById,
        senderRole: createdByRole,
        context: {
          entityType: 'user',
          entityId: userId,
          actionUrl: 'staff-management',
          metadata: { user_name: userName, user_role: userRole }
        }
      }
    );
  }

  // Notify about book distribution
  static async notifyBookDistribution(
    distributionId: string,
    schoolName: string,
    bookTitle: string,
    quantity: number,
    distributedById?: string,
    distributedByRole?: string
  ): Promise<void> {
    // Notify admins and program officers
    const recipients = await this.getUsersByRole(['admin', 'program_officer']);

    await this.createNotification(
      'book_distribution_created',
      'New Book Distribution',
      `Books have been distributed to ${schoolName}: ${quantity} x ${bookTitle}`,
      recipients,
      {
        priority: 'normal',
        senderId: distributedById,
        senderRole: distributedByRole,
        context: {
          entityType: 'distribution',
          entityId: distributionId,
          actionUrl: 'books',
          metadata: { school_name: schoolName, book_title: bookTitle, quantity }
        }
      }
    );
  }

  // Notify about low inventory
  static async notifyLowInventory(
    bookId: string,
    bookTitle: string,
    currentQuantity: number,
    threshold: number
  ): Promise<void> {
    // Notify admins and program officers about low inventory
    const recipients = await this.getUsersByRole(['admin', 'program_officer']);

    await this.createNotification(
      'low_inventory',
      'Low Inventory Alert',
      `Book inventory is running low: ${bookTitle} (${currentQuantity} remaining, threshold: ${threshold})`,
      recipients,
      {
        priority: 'high',
        context: {
          entityType: 'book',
          entityId: bookId,
          actionUrl: 'books',
          metadata: { book_title: bookTitle, current_quantity: currentQuantity, threshold }
        }
      }
    );
  }

  // Send system update notifications
  static async notifySystemUpdate(
    title: string,
    message: string,
    targetRoles?: string[]
  ): Promise<void> {
    const roles = targetRoles || ['admin', 'program_officer', 'field_staff'];
    const recipients = await this.getUsersByRole(roles);

    await this.createNotification(
      'system_update',
      title,
      message,
      recipients,
      {
        priority: 'normal',
        context: {
          actionUrl: 'dashboard'
        }
      }
    );
  }

  // Send check-in reminders to field staff
  static async notifyCheckInReminder(staffId: string): Promise<void> {
    const { data: staff } = await supabase
      .from('profiles')
      .select('id, role, name')
      .eq('id', staffId)
      .eq('role', 'field_staff')
      .single();

    if (!staff) return;

    await this.createNotification(
      'check_in_reminder',
      'Check-in Reminder',
      'Don\'t forget to check in at your assigned school today',
      [{ id: staff.id, role: staff.role, name: staff.name }],
      {
        priority: 'normal',
        context: {
          actionUrl: 'field-visits'
        },
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // Expires in 24 hours
      }
    );
  }
}
