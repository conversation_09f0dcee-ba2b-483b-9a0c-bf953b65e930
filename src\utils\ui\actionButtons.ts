import { LucideIcon, Plus, Edit, Trash2, Save, Download, Upload, Settings, Eye, EyeOff, RefreshCw, Search, Filter, MoreHorizontal, X, Check } from 'lucide-react';
import { PageHeaderAction } from '@/components/layout/PageHeader';

/**
 * Standard action button configurations for consistent UI patterns
 */

export interface StandardActionConfig {
  label: string;
  icon: LucideIcon;
  variant: PageHeaderAction['variant'];
  priority: PageHeaderAction['priority'];
  className?: string;
  tooltip?: string;
}

// Common action button configurations
export const standardActions: Record<string, StandardActionConfig> = {
  // Primary Actions
  create: {
    label: 'Create',
    icon: Plus,
    variant: 'default',
    priority: 'primary',
    className: 'bg-ilead-green hover:bg-ilead-dark-green text-white',
    tooltip: 'Create new item'
  },
  
  save: {
    label: 'Save',
    icon: Save,
    variant: 'default',
    priority: 'primary',
    className: 'bg-ilead-green hover:bg-ilead-dark-green text-white',
    tooltip: 'Save changes'
  },

  // Secondary Actions
  edit: {
    label: 'Edit',
    icon: Edit,
    variant: 'outline',
    priority: 'secondary',
    tooltip: 'Edit item'
  },

  view: {
    label: 'View',
    icon: Eye,
    variant: 'outline',
    priority: 'secondary',
    tooltip: 'View details'
  },

  download: {
    label: 'Download',
    icon: Download,
    variant: 'outline',
    priority: 'secondary',
    tooltip: 'Download file'
  },

  upload: {
    label: 'Upload',
    icon: Upload,
    variant: 'outline',
    priority: 'secondary',
    tooltip: 'Upload file'
  },

  refresh: {
    label: 'Refresh',
    icon: RefreshCw,
    variant: 'outline',
    priority: 'secondary',
    tooltip: 'Refresh data'
  },

  // Tertiary Actions
  search: {
    label: 'Search',
    icon: Search,
    variant: 'ghost',
    priority: 'tertiary',
    tooltip: 'Search items'
  },

  filter: {
    label: 'Filter',
    icon: Filter,
    variant: 'ghost',
    priority: 'tertiary',
    tooltip: 'Filter results'
  },

  settings: {
    label: 'Settings',
    icon: Settings,
    variant: 'ghost',
    priority: 'tertiary',
    tooltip: 'Open settings'
  },

  more: {
    label: 'More',
    icon: MoreHorizontal,
    variant: 'ghost',
    priority: 'tertiary',
    tooltip: 'More actions'
  },

  // Destructive Actions
  delete: {
    label: 'Delete',
    icon: Trash2,
    variant: 'destructive',
    priority: 'secondary',
    tooltip: 'Delete item'
  },

  cancel: {
    label: 'Cancel',
    icon: X,
    variant: 'outline',
    priority: 'tertiary',
    tooltip: 'Cancel action'
  },

  // Status Actions
  approve: {
    label: 'Approve',
    icon: Check,
    variant: 'default',
    priority: 'primary',
    className: 'bg-green-600 hover:bg-green-700 text-white',
    tooltip: 'Approve item'
  },

  hide: {
    label: 'Hide',
    icon: EyeOff,
    variant: 'outline',
    priority: 'tertiary',
    tooltip: 'Hide item'
  }
};

/**
 * Create a standardized action button configuration
 */
export function createAction(
  type: keyof typeof standardActions,
  onClick: () => void,
  overrides?: Partial<PageHeaderAction>
): PageHeaderAction {
  const config = standardActions[type];
  if (!config) {
    throw new Error(`Unknown action type: ${type}`);
  }

  return {
    label: config.label,
    icon: config.icon,
    variant: config.variant,
    priority: config.priority,
    className: config.className,
    tooltip: config.tooltip,
    onClick,
    ...overrides
  };
}

/**
 * Create multiple standardized actions
 */
export function createActions(
  actions: Array<{
    type: keyof typeof standardActions;
    onClick: () => void;
    overrides?: Partial<PageHeaderAction>;
  }>
): PageHeaderAction[] {
  return actions.map(({ type, onClick, overrides }) => 
    createAction(type, onClick, overrides)
  );
}

/**
 * Common action patterns for different page types
 */
export const actionPatterns = {
  // List page actions
  list: (handlers: {
    onCreate?: () => void;
    onRefresh?: () => void;
    onFilter?: () => void;
    onDownload?: () => void;
  }) => createActions([
    ...(handlers.onCreate ? [{ type: 'create' as const, onClick: handlers.onCreate }] : []),
    ...(handlers.onRefresh ? [{ type: 'refresh' as const, onClick: handlers.onRefresh }] : []),
    ...(handlers.onFilter ? [{ type: 'filter' as const, onClick: handlers.onFilter }] : []),
    ...(handlers.onDownload ? [{ type: 'download' as const, onClick: handlers.onDownload }] : [])
  ]),

  // Detail page actions
  detail: (handlers: {
    onEdit?: () => void;
    onDelete?: () => void;
    onDownload?: () => void;
    onBack?: () => void;
  }) => createActions([
    ...(handlers.onEdit ? [{ type: 'edit' as const, onClick: handlers.onEdit }] : []),
    ...(handlers.onDownload ? [{ type: 'download' as const, onClick: handlers.onDownload }] : []),
    ...(handlers.onDelete ? [{ type: 'delete' as const, onClick: handlers.onDelete }] : [])
  ]),

  // Form page actions
  form: (handlers: {
    onSave?: () => void;
    onCancel?: () => void;
    isLoading?: boolean;
  }) => createActions([
    ...(handlers.onSave ? [{ 
      type: 'save' as const, 
      onClick: handlers.onSave,
      overrides: { 
        loading: handlers.isLoading,
        disabled: handlers.isLoading
      }
    }] : []),
    ...(handlers.onCancel ? [{ type: 'cancel' as const, onClick: handlers.onCancel }] : [])
  ]),

  // Dashboard actions
  dashboard: (handlers: {
    onRefresh?: () => void;
    onSettings?: () => void;
    onDownload?: () => void;
  }) => createActions([
    ...(handlers.onRefresh ? [{ type: 'refresh' as const, onClick: handlers.onRefresh }] : []),
    ...(handlers.onDownload ? [{ type: 'download' as const, onClick: handlers.onDownload }] : []),
    ...(handlers.onSettings ? [{ type: 'settings' as const, onClick: handlers.onSettings }] : [])
  ]),

  // Management actions (admin pages)
  management: (handlers: {
    onCreate?: () => void;
    onUpload?: () => void;
    onDownload?: () => void;
    onSettings?: () => void;
    onRefresh?: () => void;
  }) => createActions([
    ...(handlers.onCreate ? [{ type: 'create' as const, onClick: handlers.onCreate }] : []),
    ...(handlers.onUpload ? [{ type: 'upload' as const, onClick: handlers.onUpload }] : []),
    ...(handlers.onDownload ? [{ type: 'download' as const, onClick: handlers.onDownload }] : []),
    ...(handlers.onRefresh ? [{ type: 'refresh' as const, onClick: handlers.onRefresh }] : []),
    ...(handlers.onSettings ? [{ type: 'settings' as const, onClick: handlers.onSettings }] : [])
  ])
};

/**
 * Mobile-optimized action configurations
 */
export const mobileActionPatterns = {
  // Simplified actions for mobile
  listMobile: (handlers: {
    onCreate?: () => void;
    onFilter?: () => void;
  }) => createActions([
    ...(handlers.onCreate ? [{ 
      type: 'create' as const, 
      onClick: handlers.onCreate,
      overrides: { mobileHidden: false }
    }] : []),
    ...(handlers.onFilter ? [{ 
      type: 'filter' as const, 
      onClick: handlers.onFilter,
      overrides: { mobileHidden: false }
    }] : [])
  ]),

  // Essential actions only for mobile forms
  formMobile: (handlers: {
    onSave?: () => void;
    onCancel?: () => void;
    isLoading?: boolean;
  }) => createActions([
    ...(handlers.onSave ? [{ 
      type: 'save' as const, 
      onClick: handlers.onSave,
      overrides: { 
        loading: handlers.isLoading,
        disabled: handlers.isLoading,
        mobileHidden: false
      }
    }] : []),
    ...(handlers.onCancel ? [{ 
      type: 'cancel' as const, 
      onClick: handlers.onCancel,
      overrides: { mobileHidden: false }
    }] : [])
  ])
};

/**
 * Responsive action configuration helper
 */
export function createResponsiveActions(
  desktopActions: PageHeaderAction[],
  mobileActions?: PageHeaderAction[]
): PageHeaderAction[] {
  if (!mobileActions) {
    // Auto-generate mobile actions by prioritizing primary actions
    mobileActions = desktopActions
      .filter(action => action.priority === 'primary')
      .slice(0, 2); // Limit to 2 primary actions on mobile
  }

  return desktopActions.map(action => {
    const isMobileAction = mobileActions!.some(ma => ma.label === action.label);
    return {
      ...action,
      mobileHidden: !isMobileAction
    };
  });
}
