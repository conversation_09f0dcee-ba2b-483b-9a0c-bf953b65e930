-- Create user notification preferences table
CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Delivery methods
    email_notifications BOOLEAN DEFAULT true,
    push_notifications B<PERSON><PERSON>EA<PERSON> DEFAULT true,
    sms_notifications B<PERSON><PERSON><PERSON>N DEFAULT false,
    
    -- Notification types
    task_reminders BOOLEAN DEFAULT true,
    attendance_alerts BOOLEAN DEFAULT true,
    report_notifications BOOLEAN DEFAULT true,
    system_updates BOOLEAN DEFAULT true,
    weekly_summary BOOLEAN DEFAULT true,
    
    -- Timing preferences
    notification_frequency TEXT DEFAULT 'immediate' CHECK (notification_frequency IN ('immediate', 'daily', 'weekly')),
    quiet_hours_start TIME DEFAULT '22:00',
    quiet_hours_end TIME DEFAULT '07:00',
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one record per user
    UNIQUE(user_id)
);

-- Add RLS policies
ALTER TABLE user_notification_preferences ENABLE ROW LEVEL SECURITY;

-- Users can view and update their own notification preferences
CREATE POLICY "Users can view own notification preferences" ON user_notification_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notification preferences" ON user_notification_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notification preferences" ON user_notification_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can view all notification preferences
CREATE POLICY "Admins can view all notification preferences" ON user_notification_preferences
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_user_id ON user_notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_notification_preferences_notification_frequency ON user_notification_preferences(notification_frequency);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_user_notification_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_notification_preferences_updated_at
    BEFORE UPDATE ON user_notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_notification_preferences_updated_at();

-- Add avatar_url column to profiles table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'avatar_url'
    ) THEN
        ALTER TABLE profiles ADD COLUMN avatar_url TEXT;
    END IF;
END $$;

-- Add bio column to profiles table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'bio'
    ) THEN
        ALTER TABLE profiles ADD COLUMN bio TEXT;
    END IF;
END $$;

-- Add timezone column to profiles table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'timezone'
    ) THEN
        ALTER TABLE profiles ADD COLUMN timezone TEXT DEFAULT 'Africa/Kampala';
    END IF;
END $$;

-- Create avatars storage bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Set up storage policies for avatars
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
    FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'avatars' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own avatar" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'avatars' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own avatar" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'avatars' 
        AND auth.uid()::text = (storage.foldername(name))[1]
    );

-- Function to get user notification preferences with defaults
CREATE OR REPLACE FUNCTION get_user_notification_preferences(user_id UUID)
RETURNS TABLE (
    email_notifications BOOLEAN,
    push_notifications BOOLEAN,
    sms_notifications BOOLEAN,
    task_reminders BOOLEAN,
    attendance_alerts BOOLEAN,
    report_notifications BOOLEAN,
    system_updates BOOLEAN,
    weekly_summary BOOLEAN,
    notification_frequency TEXT,
    quiet_hours_start TIME,
    quiet_hours_end TIME
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(unp.email_notifications, true) as email_notifications,
        COALESCE(unp.push_notifications, true) as push_notifications,
        COALESCE(unp.sms_notifications, false) as sms_notifications,
        COALESCE(unp.task_reminders, true) as task_reminders,
        COALESCE(unp.attendance_alerts, true) as attendance_alerts,
        COALESCE(unp.report_notifications, true) as report_notifications,
        COALESCE(unp.system_updates, true) as system_updates,
        COALESCE(unp.weekly_summary, true) as weekly_summary,
        COALESCE(unp.notification_frequency, 'immediate') as notification_frequency,
        COALESCE(unp.quiet_hours_start, '22:00'::TIME) as quiet_hours_start,
        COALESCE(unp.quiet_hours_end, '07:00'::TIME) as quiet_hours_end
    FROM user_notification_preferences unp
    WHERE unp.user_id = get_user_notification_preferences.user_id
    
    UNION ALL
    
    -- Return defaults if no preferences exist
    SELECT 
        true as email_notifications,
        true as push_notifications,
        false as sms_notifications,
        true as task_reminders,
        true as attendance_alerts,
        true as report_notifications,
        true as system_updates,
        true as weekly_summary,
        'immediate' as notification_frequency,
        '22:00'::TIME as quiet_hours_start,
        '07:00'::TIME as quiet_hours_end
    WHERE NOT EXISTS (
        SELECT 1 FROM user_notification_preferences unp2 
        WHERE unp2.user_id = get_user_notification_preferences.user_id
    )
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
