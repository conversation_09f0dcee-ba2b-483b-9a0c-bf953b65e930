import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Calendar,
  BookOpen,
  School,
  LogIn,
  LogOut,
  Plus,
  Target,
  TrendingUp,
  Users,
  FileText
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useRecentTasks, useCreateTask, TaskFormData } from '@/hooks/tasks';
import { useToast } from '@/hooks/use-toast';
import { useUserActivities, formatActivityTime, getActivityIcon, getActivityColor } from '@/hooks/activities';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { FieldStaffCheckInModal, FieldStaffCheckOutModal } from '../field-staff';
import CreateTaskDialog from '../CreateTaskDialog';

interface FieldStaffDashboardProps {
  onViewChange?: (view: string) => void;
}

interface FieldStaffMetrics {
  totalVisits: number;
  completedTasks: number;
  pendingTasks: number;
  thisWeekVisits: number;
  averageVisitDuration: number;
  lastCheckIn?: string;
  currentSchool?: string;
}

export const FieldStaffDashboard: React.FC<FieldStaffDashboardProps> = ({ onViewChange }) => {
  const { profile } = useAuth();
  const { data: unifiedStatus } = useUnifiedCheckInStatus();
  const { toast } = useToast();

  // Modal states
  const [checkInModalOpen, setCheckInModalOpen] = useState(false);
  const [checkOutModalOpen, setCheckOutModalOpen] = useState(false);
  const [createTaskModalOpen, setCreateTaskModalOpen] = useState(false);

  // Task creation
  const createTaskMutation = useCreateTask();

  const handleCreateTask = async (taskData: TaskFormData) => {
    try {
      await createTaskMutation.mutateAsync(taskData);
      toast({
        title: "Success",
        description: "Task created successfully",
      });
      setCreateTaskModalOpen(false);
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error).message || "Failed to create task",
        variant: "destructive",
      });
    }
  };

  // Fetch field staff specific metrics
  const { data: metrics } = useQuery<FieldStaffMetrics>({
    queryKey: ['field-staff-metrics', profile?.id],
    queryFn: async () => {
      if (!profile?.id) throw new Error('No user profile');
      
      // Get field visits count
      const { data: visits } = await supabase
        .from('field_visits')
        .select('id, created_at, duration_minutes')
        .eq('staff_id', profile.id);

      // Get tasks
      const { data: tasks } = await supabase
        .from('tasks')
        .select('id, status')
        .eq('assigned_to', profile.id);

      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const thisWeekVisits = visits?.filter(v => 
        new Date(v.created_at) >= weekAgo
      ).length || 0;

      const completedTasks = tasks?.filter(t => t.status === 'completed').length || 0;
      const pendingTasks = tasks?.filter(t => t.status !== 'completed').length || 0;

      const totalDuration = visits?.reduce((sum, v) => sum + (v.duration_minutes || 0), 0) || 0;
      const averageVisitDuration = visits?.length ? Math.round(totalDuration / visits.length) : 0;

      return {
        totalVisits: visits?.length || 0,
        completedTasks,
        pendingTasks,
        thisWeekVisits,
        averageVisitDuration,
        lastCheckIn: unifiedStatus?.lastCheckIn,
        currentSchool: unifiedStatus?.currentSchool?.name
      };
    },
    enabled: !!profile?.id
  });

  // Fetch recent tasks
  const { data: tasks = [] } = useRecentTasks(5);

  // Fetch recent activities for this user
  const { data: activities = [], isLoading: activitiesLoading, error: activitiesError } = useUserActivities(profile?.id || '', 5);

  // Quick stats for field staff
  const quickStats = [
    {
      title: 'Total Visits',
      value: metrics?.totalVisits || 0,
      icon: MapPin,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      onClick: () => onViewChange?.('field-visits')
    },
    {
      title: 'Completed Tasks',
      value: metrics?.completedTasks || 0,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      onClick: () => onViewChange?.('tasks')
    },
    {
      title: 'Pending Tasks',
      value: metrics?.pendingTasks || 0,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      onClick: () => onViewChange?.('tasks')
    },
    {
      title: 'This Week',
      value: metrics?.thisWeekVisits || 0,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      onClick: () => onViewChange?.('field-visits')
    }
  ];

  return (
    <PageLayout>
      <PageHeader
        title="My Dashboard"
        description="Track your field activities and manage your tasks"
        icon={Target}
        actions={[
          // Check-in/out actions
          ...(unifiedStatus?.isCheckedIn ? [{
            label: 'Check Out & Report',
            onClick: () => setCheckOutModalOpen(true),
            icon: LogOut,
            variant: 'destructive' as const,
            className: 'bg-red-600 text-white hover:bg-red-700 btn-mobile'
          }] : [{
            label: 'Check In',
            onClick: () => setCheckInModalOpen(true),
            icon: LogIn,
            variant: 'default' as const,
            className: 'bg-green-600 text-white hover:bg-green-700 btn-mobile'
          }]),
          {
            label: 'New Task',
            onClick: () => setCreateTaskModalOpen(true),
            icon: Plus,
            variant: 'default' as const,
            className: 'bg-ilead-green hover:bg-ilead-dark-green text-white btn-mobile'
          }
        ]}
      />

      {/* Current Status Card */}
      <ContentCard>
        <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-full ${unifiedStatus?.isCheckedIn ? 'bg-green-100' : 'bg-gray-100'}`}>
              <MapPin className={`h-6 w-6 ${unifiedStatus?.isCheckedIn ? 'text-green-600' : 'text-gray-400'}`} />
            </div>
            <div>
              <h3 className="font-semibold text-lg">
                {unifiedStatus?.isCheckedIn ? 'Currently Checked In' : 'Ready to Start'}
              </h3>
              <p className="text-gray-600">
                {unifiedStatus?.isCheckedIn 
                  ? `At ${metrics?.currentSchool || 'Unknown Location'}`
                  : 'Check in to begin your field activities'
                }
              </p>
              {unifiedStatus?.isCheckedIn && metrics?.lastCheckIn && (
                <p className="text-sm text-gray-500">
                  Since {new Date(metrics.lastCheckIn).toLocaleTimeString()}
                </p>
              )}
            </div>
          </div>
          <Badge variant={unifiedStatus?.isCheckedIn ? 'default' : 'secondary'} className="text-sm">
            {unifiedStatus?.isCheckedIn ? 'Active' : 'Offline'}
          </Badge>
        </div>
      </ContentCard>

      {/* Quick Stats */}
      <ContentCard noPadding>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 p-6">
          {quickStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card
                key={index}
                className="border-l-4 border-l-ilead-green cursor-pointer hover:shadow-md transition-shadow card-mobile"
                onClick={stat.onClick}
              >
                <CardContent className="p-4 flex items-center">
                  <div className={`${stat.bgColor} p-3 rounded-lg mr-4`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </ContentCard>

      {/* Quick Actions */}
      <ContentCard title="Quick Actions">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <Button
            variant="outline"
            className="w-full bg-blue-50 hover:bg-blue-100 border-blue-200 btn-mobile"
            onClick={() => onViewChange?.('field-visits')}
          >
            <MapPin className="h-4 w-4 mr-2" />
            View My Visits
          </Button>
          <Button
            variant="outline"
            className="w-full bg-green-50 hover:bg-green-100 border-green-200 btn-mobile"
            onClick={() => onViewChange?.('tasks')}
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Manage Tasks
          </Button>
          <Button
            variant="outline"
            className="w-full bg-purple-50 hover:bg-purple-100 border-purple-200 btn-mobile"
            onClick={() => onViewChange?.('schools')}
          >
            <School className="h-4 w-4 mr-2" />
            Browse Schools
          </Button>
        </div>
      </ContentCard>

      {/* Recent Tasks and Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Tasks */}
        <ContentCard>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-ilead-green" />
                My Recent Tasks
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewChange?.('tasks')}
                className="text-ilead-green hover:text-ilead-dark-green"
              >
                View All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {tasks.slice(0, 3).map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                  onClick={() => onViewChange?.('tasks')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      task.priority === 'urgent' ? 'bg-red-500' :
                      task.priority === 'high' ? 'bg-orange-500' :
                      task.priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                    }`} />
                    <div>
                      <p className="font-medium text-gray-900">{task.title}</p>
                      <p className="text-sm text-gray-600">
                        {task.due_date ? `Due ${new Date(task.due_date).toLocaleDateString()}` : 'No due date'}
                      </p>
                    </div>
                  </div>
                  <Badge variant={task.status === 'completed' ? 'default' : 'secondary'}>
                    {task.status}
                  </Badge>
                </div>
              ))}
              {tasks.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No tasks assigned yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </ContentCard>

        {/* Recent Activity */}
        <ContentCard>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                Recent Activity
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewChange?.('field-visits')}
                className="text-blue-600 hover:text-blue-700"
              >
                View All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              {activitiesLoading ? (
                // Loading state
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse flex-shrink-0" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded animate-pulse" />
                        <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : activitiesError ? (
                // Error state
                <div className="text-center py-8 text-gray-500">
                  <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-300" />
                  <p>Unable to load activities</p>
                </div>
              ) : activities.length === 0 ? (
                // Empty state
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No recent activity</p>
                </div>
              ) : (
                // Activities list
                activities.slice(0, 3).map((activity) => {
                  const iconEmoji = getActivityIcon(activity.activity_type);
                  const colorClasses = getActivityColor(activity.activity_type);

                  return (
                    <div
                      key={activity.id}
                      className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                      onClick={() => onViewChange?.('field-visits')}
                    >
                      <div className={`p-2 rounded-full ${colorClasses} flex-shrink-0`}>
                        <span className="text-sm">{iconEmoji}</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900">
                          {activity.description}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatActivityTime(activity.created_at)}
                        </p>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </CardContent>
        </ContentCard>
      </div>

      {/* Modals */}
      <FieldStaffCheckInModal
        isOpen={checkInModalOpen}
        onClose={() => setCheckInModalOpen(false)}
      />
      <FieldStaffCheckOutModal
        isOpen={checkOutModalOpen}
        onClose={() => setCheckOutModalOpen(false)}
      />
      <CreateTaskDialog
        open={createTaskModalOpen}
        onOpenChange={setCreateTaskModalOpen}
        onSubmit={handleCreateTask}
        loading={createTaskMutation.isPending}
        canAssignTasks={false}
      />
    </PageLayout>
  );
};
