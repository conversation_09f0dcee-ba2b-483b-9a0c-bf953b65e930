import { 
  Home, 
  User<PERSON>he<PERSON>, 
  CheckSquare, 
  School, 
  BookOpen, 
  Award, 
  Users, 
  Settings, 
  HelpCircle,
  FileText,
  BarChart3,
  type LucideIcon
} from 'lucide-react';
import { type UserRole } from '@/utils/rbac';

export interface NavigationItem {
  id: string;
  label: string | ((role: UserRole) => string); // Allow dynamic labels based on role
  icon: LucideIcon;
  route: string;
  roles: UserRole[];
  description?: string;
  children?: NavigationItem[];
  adminOnly?: boolean;
  condition?: (role: UserRole) => boolean; // Custom condition for showing item
  priority?: number; // For ordering items
}

export interface NavigationSection {
  id: string;
  label: string;
  items: NavigationItem[];
  roles: UserRole[];
  order: number;
}

/**
 * Simplified navigation structure
 * Each item represents a distinct feature area with clear role-based access
 */
export const navigationConfig: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home,
    route: 'dashboard',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Overview and key metrics',
    priority: 1,
  },
  {
    id: 'field-visits',
    label: (role: UserRole) => role === 'field_staff' ? 'My Field Visits' : 'Field Visits',
    icon: UserCheck,
    route: 'field-visits',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Check-in, reports, and field activities',
    priority: 2,
  },
  {
    id: 'tasks',
    label: 'Tasks',
    icon: CheckSquare,
    route: 'tasks',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Task management and assignments',
    priority: 3,
  },
  {
    id: 'schools',
    label: 'Schools',
    icon: School,
    route: 'schools',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'School management and information',
    priority: 4,
  },
  {
    id: 'books',
    label: 'Books',
    icon: BookOpen,
    route: 'books',
    roles: ['admin', 'program_officer'],
    description: 'Book management and distributions',
    priority: 5,
  },
  {
    id: 'impact',
    label: 'Impact',
    icon: Award,
    route: 'impact',
    roles: ['admin'],
    description: 'Impact measurement and analytics',
    adminOnly: true,
    priority: 6,
    condition: (role: UserRole) => role === 'admin',
    children: [
      {
        id: 'impact-overview',
        label: 'Overview',
        icon: BarChart3,
        route: 'impact',
        roles: ['admin'],
      },
      {
        id: 'impact-students',
        label: 'Student Outcomes',
        icon: Users,
        route: 'impact-students',
        roles: ['admin'],
      },
      {
        id: 'impact-feedback',
        label: 'Beneficiary Feedback',
        icon: FileText,
        route: 'impact-feedback',
        roles: ['admin'],
      },
      {
        id: 'impact-reports',
        label: 'Reports',
        icon: FileText,
        route: 'impact-reports',
        roles: ['admin'],
      },
    ],
  },
  {
    id: 'staff-management',
    label: 'Staff Management',
    icon: Users,
    route: 'staff-management',
    roles: ['admin', 'program_officer'],
    description: 'User management and permissions',
    priority: 7,
    condition: (role: UserRole) => role === 'admin' || role === 'program_officer',
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    route: 'settings',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Profile settings and preferences',
    priority: 8,
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: HelpCircle,
    route: 'help',
    roles: ['admin', 'program_officer', 'field_staff'],
    description: 'Documentation and support',
    priority: 9,
    children: [
      {
        id: 'help-docs',
        label: 'Documentation',
        icon: FileText,
        route: 'help-docs',
        roles: ['admin'],
      },
    ],
  },
];

/**
 * Route mapping for simplified navigation
 * Maps routes to their corresponding components
 */
export const routeMapping: Record<string, string> = {
  // Core routes
  'dashboard': 'Dashboard',
  'field-visits': 'UnifiedFieldVisits',
  'staff-reports': 'UnifiedFieldVisits',
  'tasks': 'UnifiedTaskManagement',
  'schools': 'SchoolList',
  'books': 'Books',
  'staff-management': 'StaffManagement',
  'settings': 'Settings',
  'help': 'Help',
  
  // Impact routes
  'impact': 'ImpactOverview',
  'impact-students': 'StudentOutcomes',
  'impact-feedback': 'BeneficiaryFeedback',
  'impact-reports': 'ImpactReports',
  
  // Help routes
  'help-docs': 'Documentation',
};

/**
 * Legacy route redirects
 * Maps old routes to new simplified routes
 */
export const legacyRouteRedirects: Record<string, string> = {
  // Attendance redirects to field-visits
  'attendance': 'field-visits',
  'attendance-sessions': 'field-visits',
  'session-management': 'field-visits',
  'attendance-gps': 'field-visits',
  'field-staff-checkin': 'field-visits',
  'field-staff-checkout': 'field-visits',
  
  // Task management consolidation
  'tasks-list': 'tasks',
  'tasks-assigned': 'tasks',
  'tasks-managed': 'tasks',
  'tasks-completed': 'tasks',
  'tasks-overdue': 'tasks',
  
  // School management consolidation
  'schools-list': 'schools',
  'schools-primary': 'schools',
  'schools-secondary': 'schools',
  'schools-inactive': 'schools',
  'schools-locations': 'schools',
  
  // Book management consolidation
  'book-management': 'books',
  'book-distributions': 'books',
  'distributions': 'books',
  'distributions-active': 'books',
  'distributions-history': 'books',
  
  // Reports consolidation under impact
  'reports': 'impact-reports',
  'attendance-reports': 'impact-reports',
  'field-staff-reports': 'impact-reports',
  'field-staff-analytics': 'impact-reports',
  
  // Settings consolidation
  'settings-profile': 'settings',
  'settings-notifications': 'settings',
};

/**
 * Enhanced navigation utilities with centralized role-based logic
 */

/**
 * Get navigation items for a specific user role with enhanced filtering
 */
export function getNavigationForRole(userRole: UserRole): NavigationItem[] {
  return navigationConfig
    .filter(item => {
      // Check basic role access
      if (!item.roles.includes(userRole)) return false;

      // Check admin-only flag
      if (item.adminOnly && userRole !== 'admin') return false;

      // Check custom condition
      if (item.condition && !item.condition(userRole)) return false;

      return true;
    })
    .map(item => ({
      ...item,
      // Resolve dynamic labels
      label: typeof item.label === 'function' ? item.label(userRole) : item.label,
      // Filter children based on role
      children: item.children?.filter(child => {
        if (!child.roles.includes(userRole)) return false;
        if (child.adminOnly && userRole !== 'admin') return false;
        if (child.condition && !child.condition(userRole)) return false;
        return true;
      }).map(child => ({
        ...child,
        label: typeof child.label === 'function' ? child.label(userRole) : child.label,
      }))
    }))
    .sort((a, b) => (a.priority || 999) - (b.priority || 999)); // Sort by priority
}

/**
 * Check if a route is accessible for a user role with enhanced logic
 */
export function canAccessRoute(userRole: UserRole, route: string): boolean {
  // Check direct routes
  const directAccess = navigationConfig.some(item => {
    if (item.route !== route) return false;
    if (!item.roles.includes(userRole)) return false;
    if (item.adminOnly && userRole !== 'admin') return false;
    if (item.condition && !item.condition(userRole)) return false;
    return true;
  });

  if (directAccess) return true;

  // Check child routes
  return navigationConfig.some(item =>
    item.children?.some(child => {
      if (child.route !== route) return false;
      if (!child.roles.includes(userRole)) return false;
      if (child.adminOnly && userRole !== 'admin') return false;
      if (child.condition && !child.condition(userRole)) return false;
      return true;
    })
  );
}

/**
 * Get navigation sections organized by role and priority
 */
export function getNavigationSections(userRole: UserRole): NavigationSection[] {
  const items = getNavigationForRole(userRole);

  // Group items into logical sections
  const sections: NavigationSection[] = [
    {
      id: 'main',
      label: 'Main Navigation',
      items: items.filter(item => ['dashboard', 'field-visits', 'tasks'].includes(item.id)),
      roles: ['admin', 'program_officer', 'field_staff'],
      order: 1,
    },
    {
      id: 'management',
      label: 'Management',
      items: items.filter(item => ['schools', 'books', 'staff-management'].includes(item.id)),
      roles: ['admin', 'program_officer'],
      order: 2,
    },
    {
      id: 'analytics',
      label: 'Analytics & Reports',
      items: items.filter(item => ['impact'].includes(item.id)),
      roles: ['admin'],
      order: 3,
    },
    {
      id: 'system',
      label: 'System',
      items: items.filter(item => ['settings', 'help'].includes(item.id)),
      roles: ['admin', 'program_officer', 'field_staff'],
      order: 4,
    },
  ];

  return sections
    .filter(section =>
      section.roles.includes(userRole) &&
      section.items.length > 0
    )
    .sort((a, b) => a.order - b.order);
}

/**
 * Check if user has access to a specific navigation feature
 */
export function hasNavigationAccess(userRole: UserRole, featureId: string): boolean {
  const item = navigationConfig.find(item => item.id === featureId);
  if (!item) return false;

  if (!item.roles.includes(userRole)) return false;
  if (item.adminOnly && userRole !== 'admin') return false;
  if (item.condition && !item.condition(userRole)) return false;

  return true;
}

/**
 * Get navigation item by ID with role-based filtering
 */
export function getNavigationItem(userRole: UserRole, itemId: string): NavigationItem | null {
  const item = navigationConfig.find(item => item.id === itemId);
  if (!item) return null;

  if (!hasNavigationAccess(userRole, itemId)) return null;

  return {
    ...item,
    label: typeof item.label === 'function' ? item.label(userRole) : item.label,
    children: item.children?.filter(child => {
      if (!child.roles.includes(userRole)) return false;
      if (child.adminOnly && userRole !== 'admin') return false;
      if (child.condition && !child.condition(userRole)) return false;
      return true;
    }).map(child => ({
      ...child,
      label: typeof child.label === 'function' ? child.label(userRole) : child.label,
    }))
  };
}

/**
 * Get the component name for a route
 */
export function getComponentForRoute(route: string): string | null {
  // Check for legacy redirects first
  const redirectedRoute = legacyRouteRedirects[route] || route;
  return routeMapping[redirectedRoute] || null;
}

/**
 * Get breadcrumb path for a route
 */
export function getBreadcrumbPath(route: string): NavigationItem[] {
  const path: NavigationItem[] = [];
  
  for (const item of navigationConfig) {
    if (item.route === route) {
      path.push(item);
      break;
    }
    
    if (item.children) {
      const child = item.children.find(child => child.route === route);
      if (child) {
        path.push(item, child);
        break;
      }
    }
  }
  
  return path;
}
