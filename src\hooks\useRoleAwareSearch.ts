import { useState, useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';

export interface SearchResult {
  id: string;
  type: 'school' | 'task' | 'field_report' | 'book' | 'user' | 'distribution';
  title: string;
  description?: string;
  metadata?: Record<string, string | number | boolean>;
  url?: string;
  relevance?: number;
}

export interface SearchFilters {
  types?: SearchResult['type'][];
  dateFrom?: string;
  dateTo?: string;
  status?: string;
}

interface UseRoleAwareSearchProps {
  query: string;
  filters?: SearchFilters;
  limit?: number;
  enabled?: boolean;
}

export const useRoleAwareSearch = ({
  query,
  filters = {},
  limit = 20,
  enabled = true
}: UseRoleAwareSearchProps) => {
  const { user, profile } = useAuth();
  const { roleChecker } = useAccessControl();

  // Determine what types of content the user can search based on their role
  const allowedSearchTypes = useMemo(() => {
    const types: SearchResult['type'][] = [];

    // All roles can search schools and their own tasks
    types.push('school', 'task');

    // Field staff can search their own field reports
    if (roleChecker.isFieldStaff()) {
      types.push('field_report');
    }

    // Program officers and admins can search all field reports and users
    if (roleChecker.isAdminOrProgramOfficer()) {
      types.push('field_report', 'user');
    }

    // Program officers and admins can search books
    if (roleChecker.canManageBooks()) {
      types.push('book');
    }

    // Admins can search distributions
    if (roleChecker.isAdmin()) {
      types.push('distribution');
    }

    return types;
  }, [roleChecker]);

  // Filter requested types by what the user is allowed to search
  const searchTypes = useMemo(() => {
    if (filters.types) {
      return filters.types.filter(type => allowedSearchTypes.includes(type));
    }
    return allowedSearchTypes;
  }, [filters.types, allowedSearchTypes]);

  return useQuery({
    queryKey: ['role-aware-search', query, searchTypes, filters, user?.id, profile?.role],
    queryFn: async (): Promise<SearchResult[]> => {
      if (!query.trim() || !user || !profile) {
        return [];
      }

      const results: SearchResult[] = [];
      const searchTerm = query.trim().toLowerCase();

      // Search Schools (all roles)
      if (searchTypes.includes('school')) {
        try {
          const { data: schools } = await supabase
            .from('schools')
            .select('id, name, district, status')
            .or(`name.ilike.%${searchTerm}%,district.ilike.%${searchTerm}%`)
            .eq('status', 'active')
            .limit(Math.ceil(limit / searchTypes.length));

          schools?.forEach(school => {
            results.push({
              id: school.id,
              type: 'school',
              title: school.name,
              description: `${school.district} - ${school.status}`,
              metadata: { district: school.district, status: school.status },
              url: 'schools'
            });
          });
        } catch (error) {
          console.error('Error searching schools:', error);
        }
      }

      // Search Tasks (role-aware)
      if (searchTypes.includes('task')) {
        try {
          let taskQuery = supabase
            .from('tasks')
            .select('id, title, description, status, assigned_to, created_by')
            .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
            .limit(Math.ceil(limit / searchTypes.length));

          // Role-based filtering for tasks
          if (profile.role === 'field_staff') {
            taskQuery = taskQuery.or(`assigned_to.eq.${user.id},created_by.eq.${user.id}`);
          }
          // Admin and program officers can see all tasks (no additional filter needed)

          const { data: tasks } = await taskQuery;

          tasks?.forEach(task => {
            results.push({
              id: task.id,
              type: 'task',
              title: task.title,
              description: task.description || `Status: ${task.status}`,
              metadata: { status: task.status },
              url: 'tasks'
            });
          });
        } catch (error) {
          console.error('Error searching tasks:', error);
        }
      }

      // Search Field Reports (role-aware)
      if (searchTypes.includes('field_report')) {
        try {
          let reportQuery = supabase
            .from('enhanced_field_reports')
            .select('id, activity_type, school_name, staff_name, report_date, staff_id')
            .or(`activity_type.ilike.%${searchTerm}%,school_name.ilike.%${searchTerm}%,staff_name.ilike.%${searchTerm}%`)
            .limit(Math.ceil(limit / searchTypes.length));

          // Field staff can only see their own reports
          if (profile.role === 'field_staff') {
            reportQuery = reportQuery.eq('staff_id', user.id);
          }
          // Admin and program officers can see all reports (no additional filter needed)

          const { data: reports } = await reportQuery;

          reports?.forEach(report => {
            results.push({
              id: report.id,
              type: 'field_report',
              title: `${report.activity_type} - ${report.school_name}`,
              description: `By ${report.staff_name} on ${new Date(report.report_date).toLocaleDateString()}`,
              metadata: { 
                activity_type: report.activity_type, 
                school_name: report.school_name,
                staff_name: report.staff_name,
                report_date: report.report_date
              },
              url: 'staff-reports'
            });
          });
        } catch (error) {
          console.error('Error searching field reports:', error);
        }
      }

      // Search Books (program officers and admins only)
      if (searchTypes.includes('book')) {
        try {
          const { data: books } = await supabase
            .from('books')
            .select('id, title, language, condition, total_quantity')
            .or(`title.ilike.%${searchTerm}%,language.ilike.%${searchTerm}%`)
            .limit(Math.ceil(limit / searchTypes.length));

          books?.forEach(book => {
            results.push({
              id: book.id,
              type: 'book',
              title: book.title,
              description: `${book.language} - ${book.condition} (${book.total_quantity} total)`,
              metadata: { 
                language: book.language, 
                condition: book.condition,
                total_quantity: book.total_quantity
              },
              url: 'books'
            });
          });
        } catch (error) {
          console.error('Error searching books:', error);
        }
      }

      // Search Users (program officers and admins only)
      if (searchTypes.includes('user')) {
        try {
          const { data: users } = await supabase
            .from('profiles')
            .select('id, name, role, country')
            .or(`name.ilike.%${searchTerm}%,role.ilike.%${searchTerm}%`)
            .limit(Math.ceil(limit / searchTypes.length));

          users?.forEach(user => {
            results.push({
              id: user.id,
              type: 'user',
              title: user.name,
              description: `${user.role} - ${user.country}`,
              metadata: { role: user.role, country: user.country },
              url: 'staff-management'
            });
          });
        } catch (error) {
          console.error('Error searching users:', error);
        }
      }

      // Sort results by relevance (simple text matching for now)
      return results.sort((a, b) => {
        const aRelevance = a.title.toLowerCase().includes(searchTerm) ? 2 : 1;
        const bRelevance = b.title.toLowerCase().includes(searchTerm) ? 2 : 1;
        return bRelevance - aRelevance;
      });
    },
    enabled: enabled && !!query.trim() && query.trim().length >= 2 && !!user && !!profile,
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
  });
};

export const useSearchSuggestions = () => {
  const { roleChecker } = useAccessControl();

  return useMemo(() => {
    const suggestions: string[] = [];

    // Common suggestions for all roles
    suggestions.push('schools', 'tasks', 'my tasks');

    if (roleChecker.isFieldStaff()) {
      suggestions.push('my reports', 'field visits');
    }

    if (roleChecker.isAdminOrProgramOfficer()) {
      suggestions.push('staff reports', 'field reports', 'users', 'staff');
    }

    if (roleChecker.canManageBooks()) {
      suggestions.push('books', 'inventory');
    }

    if (roleChecker.isAdmin()) {
      suggestions.push('distributions', 'impact');
    }

    return suggestions;
  }, [roleChecker]);
};
