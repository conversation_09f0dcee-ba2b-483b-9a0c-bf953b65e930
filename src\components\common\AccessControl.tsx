import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Lock, AlertTriangle } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';
import { type AccessControlConfig } from '@/utils/rbac';

interface AccessControlProps {
  children: React.ReactNode;
  config: AccessControlConfig;
  fallbackMessage?: string;
  showLoading?: boolean;
  loadingMessage?: string;
}

/**
 * Unified access control component
 */
export const AccessControl: React.FC<AccessControlProps> = ({
  children,
  config,
  fallbackMessage = 'You do not have permission to access this feature.',
  showLoading = true,
  loadingMessage = 'Verifying access permissions...',
}) => {
  const { checkAccess, currentUser } = useAccessControl();

  // Show loading state while authentication is being resolved
  if (!currentUser && showLoading) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">{loadingMessage}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const accessResult = checkAccess(config);

  if (!accessResult.hasAccess) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mx-auto mb-4">
              <Lock className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Restricted</h3>
            <p className="text-gray-600 mb-4">{fallbackMessage}</p>
            {accessResult.reason && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-700 mt-1">
                  <strong>Reason:</strong> {accessResult.reason}
                </p>
              </div>
            )}
            <p className="text-sm text-red-500 mt-4">
              Please contact your system administrator if you believe you should have access to this feature.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};

/**
 * Quick access control components for common scenarios
 */
export const AdminOnly: React.FC<Omit<AccessControlProps, 'config'>> = (props) => (
  <AccessControl
    {...props}
    config={{ requiredRoles: ['admin'] }}
    fallbackMessage="Access is restricted to administrators only."
  />
);

export const AdminProgramOfficer: React.FC<Omit<AccessControlProps, 'config'>> = (props) => (
  <AccessControl
    {...props}
    config={{ requiredRoles: ['admin', 'program_officer'] }}
    fallbackMessage="Access is restricted to administrators and program officers only."
  />
);

export const AllRoles: React.FC<Omit<AccessControlProps, 'config'>> = (props) => (
  <AccessControl
    {...props}
    config={{ requiredRoles: ['admin', 'program_officer', 'field_staff'] }}
    fallbackMessage="Please log in to access this feature."
  />
);

interface SelfAccessProps extends Omit<AccessControlProps, 'config'> {
  targetUserId?: string;
}

export const SelfAccess: React.FC<SelfAccessProps> = ({ targetUserId, ...props }) => (
  <AccessControl
    {...props}
    config={{
      requiredRoles: ['admin', 'program_officer', 'field_staff'],
      allowSelfAccess: true,
      targetUserId,
    }}
    fallbackMessage="You can only access your own data."
  />
);

/**
 * Role-based content component
 */
interface RoleBasedContentProps {
  adminContent?: React.ReactNode;
  programOfficerContent?: React.ReactNode;
  fieldStaffContent?: React.ReactNode;
  defaultContent?: React.ReactNode;
}

export const RoleBasedContent: React.FC<RoleBasedContentProps> = ({
  adminContent,
  programOfficerContent,
  fieldStaffContent,
  defaultContent,
}) => {
  const { currentUser } = useAccessControl();

  if (!currentUser) {
    return <>{defaultContent}</>;
  }

  switch (currentUser.role) {
    case 'admin':
      return <>{adminContent || defaultContent}</>;
    case 'program_officer':
      return <>{programOfficerContent || defaultContent}</>;
    case 'field_staff':
      return <>{fieldStaffContent || defaultContent}</>;
    default:
      return <>{defaultContent}</>;
  }
};

/**
 * Feature gate component
 */
interface FeatureGateProps {
  children: React.ReactNode;
  feature: string;
  fallback?: React.ReactNode;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  children,
  feature,
  fallback = null,
}) => {
  const { canAccessFeature } = useAccessControl();

  if (!canAccessFeature(feature)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Permission gate component
 */
interface PermissionGateProps {
  children: React.ReactNode;
  permission: string;
  fallback?: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  fallback = null,
}) => {
  const { hasPermission } = useAccessControl();

  if (!hasPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Simple access alert component
 */
interface AccessAlertProps {
  message?: string;
  type?: 'error' | 'warning' | 'info';
}

export const AccessAlert: React.FC<AccessAlertProps> = ({
  message = 'You do not have permission to access this feature.',
  type = 'error',
}) => {
  const iconMap = {
    error: Shield,
    warning: AlertTriangle,
    info: Shield,
  };

  const Icon = iconMap[type];

  return (
    <Alert>
      <Icon className="h-4 w-4" />
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
};
