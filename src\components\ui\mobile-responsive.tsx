import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

/**
 * Mobile-responsive modal that shows as full-screen on mobile and dialog on desktop
 */
interface ResponsiveModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  children: React.ReactNode;
  className?: string;
  mobileFullScreen?: boolean;
}

export const ResponsiveModal: React.FC<ResponsiveModalProps> = ({
  open,
  onOpenChange,
  title,
  children,
  className,
  mobileFullScreen = true
}) => {
  const isMobile = useIsMobile();

  if (isMobile && mobileFullScreen) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent 
          side="bottom" 
          className={cn("h-[90vh] overflow-y-auto", className)}
        >
          <SheetHeader>
            <SheetTitle>{title}</SheetTitle>
          </SheetHeader>
          <div className="mt-6">
            {children}
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn("max-w-2xl", className)}>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  );
};

/**
 * Mobile-responsive card that adapts spacing and layout
 */
interface ResponsiveCardProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  mobileCompact?: boolean;
  noPadding?: boolean;
}

export const ResponsiveCard: React.FC<ResponsiveCardProps> = ({
  children,
  title,
  description,
  className,
  mobileCompact = false,
  noPadding = false
}) => {
  return (
    <Card className={cn(
      "w-full",
      mobileCompact && "card-mobile-compact sm:card-mobile",
      className
    )}>
      {(title || description) && (
        <CardHeader className={cn(
          mobileCompact && "pb-3 sm:pb-6"
        )}>
          {title && <CardTitle className="text-mobile-heading">{title}</CardTitle>}
          {description && <p className="text-mobile-responsive text-gray-600">{description}</p>}
        </CardHeader>
      )}
      <CardContent className={cn(
        !noPadding && (mobileCompact ? "p-3 sm:p-6" : "p-4 sm:p-6"),
        noPadding && "p-0"
      )}>
        {children}
      </CardContent>
    </Card>
  );
};

/**
 * Mobile-responsive grid that stacks on mobile
 */
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
  mobileStack?: boolean;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = 3,
  gap = 'md',
  className,
  mobileStack = true
}) => {
  const gapClasses = {
    sm: 'gap-2 sm:gap-3',
    md: 'gap-3 sm:gap-4',
    lg: 'gap-4 sm:gap-6'
  };

  const columnClasses = {
    1: 'grid-cols-1',
    2: mobileStack ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-2',
    3: mobileStack ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 sm:grid-cols-3',
    4: mobileStack ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4' : 'grid-cols-2 sm:grid-cols-4'
  };

  return (
    <div className={cn(
      'grid',
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
};

/**
 * Mobile-responsive table that stacks on mobile
 */
interface ResponsiveTableProps {
  headers: string[];
  children: React.ReactNode;
  className?: string;
  mobileStack?: boolean;
}

export const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  headers,
  children,
  className,
  mobileStack = true
}) => {
  return (
    <div className={cn(
      "overflow-x-auto",
      mobileStack && "table-mobile-scroll sm:overflow-visible",
      className
    )}>
      <table className={cn(
        "w-full",
        mobileStack && "table-mobile-stack"
      )}>
        <thead>
          <tr>
            {headers.map((header, index) => (
              <th key={index} className="text-left p-3 font-medium text-gray-600 border-b">
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {children}
        </tbody>
      </table>
    </div>
  );
};

/**
 * Mobile-responsive button group
 */
interface ResponsiveButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  mobileStack?: boolean;
  mobileFullWidth?: boolean;
}

export const ResponsiveButtonGroup: React.FC<ResponsiveButtonGroupProps> = ({
  children,
  className,
  mobileStack = true,
  mobileFullWidth = false
}) => {
  return (
    <div className={cn(
      "flex gap-2",
      mobileStack && "flex-col sm:flex-row",
      mobileFullWidth && "w-full sm:w-auto",
      className
    )}>
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child) && child.type === Button) {
          return React.cloneElement(child, {
            className: cn(
              child.props.className,
              "btn-mobile",
              mobileFullWidth && "w-full sm:w-auto"
            )
          });
        }
        return child;
      })}
    </div>
  );
};

/**
 * Mobile-responsive form layout
 */
interface ResponsiveFormProps {
  children: React.ReactNode;
  className?: string;
  mobileStack?: boolean;
}

export const ResponsiveForm: React.FC<ResponsiveFormProps> = ({
  children,
  className,
  mobileStack = true
}) => {
  return (
    <form className={cn(
      "space-y-4",
      mobileStack && "spacing-mobile-normal",
      className
    )}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          // Add mobile-friendly classes to form inputs
          if (child.props.type === 'input' || child.type?.displayName === 'Input') {
            return React.cloneElement(child, {
              className: cn(child.props.className, "input-mobile")
            });
          }
          if (child.type?.displayName === 'Textarea') {
            return React.cloneElement(child, {
              className: cn(child.props.className, "textarea-mobile")
            });
          }
        }
        return child;
      })}
    </form>
  );
};

/**
 * Mobile-responsive navigation tabs
 */
interface ResponsiveTabsProps {
  tabs: Array<{
    id: string;
    label: string;
    content: React.ReactNode;
    icon?: React.ComponentType<{ className?: string }>;
  }>;
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
  mobileScrollable?: boolean;
}

export const ResponsiveTabs: React.FC<ResponsiveTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className,
  mobileScrollable = true
}) => {
  return (
    <div className={cn("w-full", className)}>
      {/* Tab Navigation */}
      <div className={cn(
        "flex border-b border-gray-200",
        mobileScrollable && "overflow-x-auto scrollbar-thin sm:overflow-visible"
      )}>
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                "flex items-center space-x-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors whitespace-nowrap btn-mobile",
                activeTab === tab.id
                  ? "border-ilead-green text-ilead-green"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              {Icon && <Icon className="h-4 w-4" />}
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="mt-4">
        {tabs.find(tab => tab.id === activeTab)?.content}
      </div>
    </div>
  );
};

/**
 * Mobile-responsive spacing component
 */
interface ResponsiveSpacingProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const ResponsiveSpacing: React.FC<ResponsiveSpacingProps> = ({
  size = 'md',
  className
}) => {
  const sizeClasses = {
    xs: 'h-2 sm:h-3',
    sm: 'h-3 sm:h-4',
    md: 'h-4 sm:h-6',
    lg: 'h-6 sm:h-8',
    xl: 'h-8 sm:h-12'
  };

  return <div className={cn(sizeClasses[size], className)} />;
};

/**
 * Hook for responsive breakpoint detection
 */
export const useResponsiveBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('mobile');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return {
    breakpoint,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet',
    isDesktop: breakpoint === 'desktop'
  };
};
