import { supabase } from '@/integrations/supabase/client';
import { validateISBN } from './bookValidation';
import { UserRole } from '@/utils/rbac';

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  existingBook?: {
    id: string;
    title: string;
    author: string;
    isbn: string;
  };
  message?: string;
}

export interface ServerValidationResult {
  hasAccess: boolean;
  reason?: string;
  errorCode?: string;
  currentRole?: UserRole;
  isOwner?: boolean;
  hasElevatedAccess?: boolean;
}

export interface EndpointValidationResult extends ServerValidationResult {
  endpointName: string;
  httpMethod: string;
  requiredRoles?: UserRole[];
  allowSelfAccess?: boolean;
  requireOwnership?: boolean;
}

/**
 * Check if ISBN already exists in the database
 */
export async function checkISBNDuplicate(isbn: string, excludeBookId?: string): Promise<DuplicateCheckResult> {
  if (!isbn || isbn.trim() === '') {
    return { isDuplicate: false };
  }

  // Validate ISBN format first
  const isbnValidation = validateISBN(isbn);
  if (!isbnValidation.isValid) {
    return { 
      isDuplicate: false, 
      message: 'Invalid ISBN format - cannot check for duplicates' 
    };
  }

  try {
    // Clean the ISBN for comparison (remove hyphens and spaces)
    const cleanISBN = isbn.replace(/[-\s]/g, '');
    
    // Query the database for existing books with this ISBN
    let query = supabase
      .from('books')
      .select('id, title, author, isbn')
      .not('isbn', 'is', null);

    // Use ilike for case-insensitive partial matching to handle different ISBN formats
    query = query.or(`isbn.ilike.%${cleanISBN}%`);

    if (excludeBookId) {
      query = query.neq('id', excludeBookId);
    }

    const { data: existingBooks, error } = await query;

    if (error) {
      console.error('Error checking ISBN duplicate:', error);
      return { 
        isDuplicate: false, 
        message: 'Unable to check for duplicate ISBN' 
      };
    }

    // Check if any of the returned books have the exact same ISBN (after cleaning)
    const duplicateBook = existingBooks?.find(book => {
      if (!book.isbn) return false;
      const existingCleanISBN = book.isbn.replace(/[-\s]/g, '');
      return existingCleanISBN === cleanISBN;
    });

    if (duplicateBook) {
      return {
        isDuplicate: true,
        existingBook: duplicateBook,
        message: `ISBN already exists for "${duplicateBook.title}" by ${duplicateBook.author}`
      };
    }

    return { isDuplicate: false };

  } catch (error) {
    console.error('Error checking ISBN duplicate:', error);
    return { 
      isDuplicate: false, 
      message: 'Unable to check for duplicate ISBN' 
    };
  }
}

/**
 * Check if a book title and author combination already exists
 */
export async function checkBookDuplicate(title: string, author: string, excludeBookId?: string): Promise<DuplicateCheckResult> {
  if (!title || !author || title.trim() === '' || author.trim() === '') {
    return { isDuplicate: false };
  }

  try {
    let query = supabase
      .from('books')
      .select('id, title, author, isbn')
      .ilike('title', title.trim())
      .ilike('author', author.trim());

    if (excludeBookId) {
      query = query.neq('id', excludeBookId);
    }

    const { data: existingBooks, error } = await query;

    if (error) {
      console.error('Error checking book duplicate:', error);
      return { 
        isDuplicate: false, 
        message: 'Unable to check for duplicate book' 
      };
    }

    if (existingBooks && existingBooks.length > 0) {
      const duplicateBook = existingBooks[0];
      return {
        isDuplicate: true,
        existingBook: duplicateBook,
        message: `A book with this title and author already exists${duplicateBook.isbn ? ` (ISBN: ${duplicateBook.isbn})` : ''}`
      };
    }

    return { isDuplicate: false };

  } catch (error) {
    console.error('Error checking book duplicate:', error);
    return { 
      isDuplicate: false, 
      message: 'Unable to check for duplicate book' 
    };
  }
}

/**
 * Comprehensive server-side validation for book creation/update
 */
export async function validateBookServerSide(
  bookData: {
    title: string;
    author: string;
    isbn?: string;
  },
  excludeBookId?: string
): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Check for ISBN duplicate if ISBN is provided
    if (bookData.isbn && bookData.isbn.trim() !== '') {
      const isbnCheck = await checkISBNDuplicate(bookData.isbn, excludeBookId);
      if (isbnCheck.isDuplicate) {
        errors.push(isbnCheck.message || 'ISBN already exists');
      } else if (isbnCheck.message) {
        warnings.push(isbnCheck.message);
      }
    }

    // Check for title/author duplicate
    const bookCheck = await checkBookDuplicate(bookData.title, bookData.author, excludeBookId);
    if (bookCheck.isDuplicate) {
      warnings.push(bookCheck.message || 'Similar book already exists');
    } else if (bookCheck.message) {
      warnings.push(bookCheck.message);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };

  } catch (error) {
    console.error('Error in server-side validation:', error);
    return {
      isValid: false,
      errors: ['Unable to validate book data'],
      warnings: []
    };
  }
}

/**
 * Server-side validation utilities for API endpoints and role-based access
 */
export class ServerValidationService {
  /**
   * Validate user role access using server-side function
   */
  static async validateUserRoleAccess(
    requiredRoles: UserRole[],
    targetUserId?: string,
    allowSelfAccess: boolean = false,
    requireOwnership: boolean = false
  ): Promise<ServerValidationResult> {
    try {
      const { data, error } = await supabase.rpc('validate_user_role_access', {
        p_required_roles: requiredRoles,
        p_target_user_id: targetUserId || null,
        p_allow_self_access: allowSelfAccess,
        p_require_ownership: requireOwnership,
      });

      if (error) {
        console.error('Server validation error:', error);
        return {
          hasAccess: false,
          reason: 'Server validation failed',
          errorCode: 'SERVER_ERROR',
        };
      }

      return {
        hasAccess: data.has_access,
        reason: data.reason,
        errorCode: data.error_code,
        currentRole: data.current_role,
        isOwner: data.is_owner,
        hasElevatedAccess: data.has_elevated_access,
      };
    } catch (error) {
      console.error('Validation request failed:', error);
      return {
        hasAccess: false,
        reason: 'Validation request failed',
        errorCode: 'REQUEST_FAILED',
      };
    }
  }

  /**
   * Validate API endpoint access using server-side function
   */
  static async validateApiEndpointAccess(
    endpointName: string,
    httpMethod: string = 'GET',
    targetUserId?: string,
    resourceId?: string
  ): Promise<EndpointValidationResult> {
    try {
      const { data, error } = await supabase.rpc('validate_api_endpoint_access', {
        p_endpoint_name: endpointName,
        p_http_method: httpMethod,
        p_target_user_id: targetUserId || null,
        p_resource_id: resourceId || null,
      });

      if (error) {
        console.error('Endpoint validation error:', error);
        return {
          hasAccess: false,
          reason: 'Endpoint validation failed',
          errorCode: 'SERVER_ERROR',
          endpointName,
          httpMethod,
        };
      }

      return {
        hasAccess: data.has_access,
        reason: data.reason,
        errorCode: data.error_code,
        currentRole: data.current_role,
        isOwner: data.is_owner,
        hasElevatedAccess: data.has_elevated_access,
        endpointName: data.endpoint_name,
        httpMethod: data.http_method,
        requiredRoles: data.required_roles,
        allowSelfAccess: data.allow_self_access,
        requireOwnership: data.require_ownership,
      };
    } catch (error) {
      console.error('Endpoint validation request failed:', error);
      return {
        hasAccess: false,
        reason: 'Endpoint validation request failed',
        errorCode: 'REQUEST_FAILED',
        endpointName,
        httpMethod,
      };
    }
  }

  /**
   * Log security events using server-side function
   */
  static async logSecurityEvent(
    eventType: string,
    endpointName?: string,
    targetUserId?: string,
    details?: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase.rpc('log_security_event', {
        p_event_type: eventType,
        p_endpoint_name: endpointName || null,
        p_target_user_id: targetUserId || null,
        p_details: details ? JSON.stringify(details) : null,
      });

      if (error) {
        console.error('Failed to log security event:', error);
      }
    } catch (error) {
      console.error('Security logging request failed:', error);
    }
  }

  /**
   * Validate field reports access
   */
  static async validateFieldReportsAccess(
    action: 'read' | 'create' | 'update' | 'delete',
    targetUserId?: string,
    reportId?: string
  ): Promise<EndpointValidationResult> {
    const endpointMap = {
      read: 'field_reports',
      create: 'field_reports_create',
      update: 'field_reports_update',
      delete: 'field_reports_delete',
    };

    const result = await this.validateApiEndpointAccess(
      endpointMap[action],
      action === 'read' ? 'GET' : 'POST',
      targetUserId,
      reportId
    );

    // Log access attempt
    if (!result.hasAccess) {
      await this.logSecurityEvent(
        'ACCESS_DENIED',
        endpointMap[action],
        targetUserId,
        { action, reportId, reason: result.reason }
      );
    }

    return result;
  }

  /**
   * Validate field staff attendance access
   */
  static async validateAttendanceAccess(
    action: 'read' | 'create' | 'update',
    targetUserId?: string,
    attendanceId?: string
  ): Promise<EndpointValidationResult> {
    const endpointMap = {
      read: 'field_staff_attendance',
      create: 'field_staff_attendance_create',
      update: 'field_staff_attendance_update',
    };

    const result = await this.validateApiEndpointAccess(
      endpointMap[action],
      action === 'read' ? 'GET' : 'POST',
      targetUserId,
      attendanceId
    );

    if (!result.hasAccess) {
      await this.logSecurityEvent(
        'ACCESS_DENIED',
        endpointMap[action],
        targetUserId,
        { action, attendanceId, reason: result.reason }
      );
    }

    return result;
  }

  /**
   * Validate tasks access
   */
  static async validateTasksAccess(
    action: 'read' | 'create' | 'update' | 'delete',
    targetUserId?: string,
    taskId?: string
  ): Promise<EndpointValidationResult> {
    const endpointMap = {
      read: 'tasks',
      create: 'tasks_create',
      update: 'tasks_update',
      delete: 'tasks_delete',
    };

    const result = await this.validateApiEndpointAccess(
      endpointMap[action],
      action === 'read' ? 'GET' : 'POST',
      targetUserId,
      taskId
    );

    if (!result.hasAccess) {
      await this.logSecurityEvent(
        'ACCESS_DENIED',
        endpointMap[action],
        targetUserId,
        { action, taskId, reason: result.reason }
      );
    }

    return result;
  }

  /**
   * Validate staff management access
   */
  static async validateStaffManagementAccess(
    action: 'read' | 'create' | 'update' | 'delete',
    targetUserId?: string
  ): Promise<EndpointValidationResult> {
    const endpointMap = {
      read: 'staff_management',
      create: 'staff_create',
      update: 'staff_update',
      delete: 'staff_delete',
    };

    const result = await this.validateApiEndpointAccess(
      endpointMap[action],
      action === 'read' ? 'GET' : 'POST',
      targetUserId
    );

    if (!result.hasAccess) {
      await this.logSecurityEvent(
        'ACCESS_DENIED',
        endpointMap[action],
        targetUserId,
        { action, reason: result.reason }
      );
    }

    return result;
  }

  /**
   * Validate profile access
   */
  static async validateProfileAccess(
    action: 'read' | 'update',
    targetUserId?: string
  ): Promise<EndpointValidationResult> {
    const endpointMap = {
      read: 'profile',
      update: 'profile_update',
    };

    const result = await this.validateApiEndpointAccess(
      endpointMap[action],
      action === 'read' ? 'GET' : 'POST',
      targetUserId
    );

    if (!result.hasAccess) {
      await this.logSecurityEvent(
        'ACCESS_DENIED',
        endpointMap[action],
        targetUserId,
        { action, reason: result.reason }
      );
    }

    return result;
  }
}

/**
 * Middleware function for validating API access
 */
export async function validateApiAccess(
  endpointName: string,
  httpMethod: string = 'GET',
  targetUserId?: string,
  resourceId?: string
): Promise<{ isValid: boolean; error?: string; details?: EndpointValidationResult }> {
  const validation = await ServerValidationService.validateApiEndpointAccess(
    endpointName,
    httpMethod,
    targetUserId,
    resourceId
  );

  if (!validation.hasAccess) {
    return {
      isValid: false,
      error: validation.reason || 'Access denied',
      details: validation,
    };
  }

  return {
    isValid: true,
    details: validation,
  };
}

/**
 * Hook for server-side validation in React components
 */
export function useServerValidation() {
  return {
    validateUserRoleAccess: ServerValidationService.validateUserRoleAccess,
    validateApiEndpointAccess: ServerValidationService.validateApiEndpointAccess,
    validateFieldReportsAccess: ServerValidationService.validateFieldReportsAccess,
    validateAttendanceAccess: ServerValidationService.validateAttendanceAccess,
    validateTasksAccess: ServerValidationService.validateTasksAccess,
    validateStaffManagementAccess: ServerValidationService.validateStaffManagementAccess,
    validateProfileAccess: ServerValidationService.validateProfileAccess,
    logSecurityEvent: ServerValidationService.logSecurityEvent,
  };
}

/**
 * Validate inventory constraints
 */
export async function validateInventoryConstraints(
  bookId: string,
  newTotalQuantity: number,
  newAvailableQuantity: number
): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Get current distribution data for this book
    const { data: distributions, error: distError } = await supabase
      .from('book_distributions')
      .select('quantity, status')
      .eq('book_id', bookId)
      .in('status', ['planned', 'in_progress']);

    if (distError) {
      warnings.push('Unable to check distribution constraints');
    } else if (distributions && distributions.length > 0) {
      const plannedQuantity = distributions.reduce((sum, dist) => sum + dist.quantity, 0);
      
      if (newAvailableQuantity < plannedQuantity) {
        errors.push(`Cannot reduce available quantity below planned distributions (${plannedQuantity} books planned)`);
      }
    }

    // Check for reasonable quantity limits
    if (newTotalQuantity > 100000) {
      warnings.push('Total quantity seems very large - please verify');
    }

    if (newAvailableQuantity > newTotalQuantity) {
      errors.push('Available quantity cannot exceed total quantity');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };

  } catch (error) {
    console.error('Error validating inventory constraints:', error);
    return {
      isValid: false,
      errors: ['Unable to validate inventory constraints'],
      warnings: []
    };
  }
}

/**
 * Validate distribution request
 */
export async function validateDistributionRequest(
  bookId: string,
  requestedQuantity: number
): Promise<{
  isValid: boolean;
  errors: string[];
  availableQuantity: number;
}> {
  const errors: string[] = [];

  try {
    // Get current inventory for the book
    const { data: inventory, error: invError } = await supabase
      .from('book_inventory')
      .select('available_quantity')
      .eq('book_id', bookId)
      .maybeSingle();

    if (invError || !inventory) {
      return {
        isValid: false,
        errors: ['Unable to check book availability'],
        availableQuantity: 0
      };
    }

    const availableQuantity = inventory.available_quantity;

    if (requestedQuantity > availableQuantity) {
      errors.push(`Insufficient inventory. Available: ${availableQuantity}, Requested: ${requestedQuantity}`);
    }

    if (requestedQuantity <= 0) {
      errors.push('Requested quantity must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors,
      availableQuantity
    };

  } catch (error) {
    console.error('Error validating distribution request:', error);
    return {
      isValid: false,
      errors: ['Unable to validate distribution request'],
      availableQuantity: 0
    };
  }
}
