import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  School, 
  BookOpen, 
  TrendingUp,
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  AlertTriangle,
  Target,
  MapPin,
  FileText,
  Plus
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useRecentTasks } from '@/hooks/tasks';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import ActivityFeed from '../ActivityFeed';

interface ProgramOfficerDashboardProps {
  onViewChange?: (view: string) => void;
}

interface ProgramMetrics {
  totalStaff: number;
  activeStaff: number;
  totalSchools: number;
  activeSchools: number;
  totalBooks: number;
  thisMonthDistributions: number;
  pendingTasks: number;
  completedTasks: number;
  fieldVisitsThisWeek: number;
  averageTaskCompletion: number;
}

export const ProgramOfficerDashboard: React.FC<ProgramOfficerDashboardProps> = ({ onViewChange }) => {
  const { profile } = useAuth();

  // Fetch program officer specific metrics
  const { data: metrics } = useQuery<ProgramMetrics>({
    queryKey: ['program-officer-metrics'],
    queryFn: async () => {
      // Get staff count
      const { data: staff } = await supabase
        .from('profiles')
        .select('id, role')
        .eq('role', 'field_staff');

      // Get schools count
      const { data: schools } = await supabase
        .from('schools')
        .select('id, registration_status');

      // Get book distributions
      const { data: distributions } = await supabase
        .from('book_distributions')
        .select('id, quantity, created_at');

      // Get tasks
      const { data: tasks } = await supabase
        .from('tasks')
        .select('id, status, created_at');

      // Get field visits
      const { data: visits } = await supabase
        .from('field_visits')
        .select('id, created_at');

      const now = new Date();
      const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1);
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const thisMonthDistributions = distributions?.filter(d => 
        new Date(d.created_at) >= monthAgo
      ).length || 0;

      const fieldVisitsThisWeek = visits?.filter(v => 
        new Date(v.created_at) >= weekAgo
      ).length || 0;

      const totalBooks = distributions?.reduce((sum, d) => sum + (d.quantity || 0), 0) || 0;
      const completedTasks = tasks?.filter(t => t.status === 'completed').length || 0;
      const pendingTasks = tasks?.filter(t => t.status !== 'completed').length || 0;
      const totalTasks = tasks?.length || 0;
      const averageTaskCompletion = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

      return {
        totalStaff: staff?.length || 0,
        activeStaff: staff?.length || 0, // Assuming all are active for now
        totalSchools: schools?.length || 0,
        activeSchools: schools?.filter(s => s.registration_status === 'active').length || 0,
        totalBooks,
        thisMonthDistributions,
        pendingTasks,
        completedTasks,
        fieldVisitsThisWeek,
        averageTaskCompletion
      };
    }
  });

  // Fetch recent tasks
  const { data: tasks = [] } = useRecentTasks(8);

  // Key metrics for program officers
  const keyMetrics = [
    {
      title: 'Field Staff',
      value: `${metrics?.activeStaff || 0}/${metrics?.totalStaff || 0}`,
      subtitle: 'Active/Total',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      onClick: () => onViewChange?.('staff-management')
    },
    {
      title: 'Active Schools',
      value: metrics?.activeSchools || 0,
      subtitle: `of ${metrics?.totalSchools || 0} total`,
      icon: School,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      onClick: () => onViewChange?.('schools')
    },
    {
      title: 'Books Distributed',
      value: metrics?.totalBooks?.toLocaleString() || '0',
      subtitle: `${metrics?.thisMonthDistributions || 0} this month`,
      icon: BookOpen,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      onClick: () => onViewChange?.('books')
    },
    {
      title: 'Task Completion',
      value: `${metrics?.averageTaskCompletion || 0}%`,
      subtitle: `${metrics?.pendingTasks || 0} pending`,
      icon: Target,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      onClick: () => onViewChange?.('tasks')
    }
  ];

  // Performance indicators
  const performanceCards = [
    {
      title: 'Field Visits This Week',
      value: metrics?.fieldVisitsThisWeek || 0,
      icon: MapPin,
      trend: '+12%',
      trendUp: true
    },
    {
      title: 'Completed Tasks',
      value: metrics?.completedTasks || 0,
      icon: CheckCircle,
      trend: '+8%',
      trendUp: true
    },
    {
      title: 'Pending Tasks',
      value: metrics?.pendingTasks || 0,
      icon: Clock,
      trend: '-5%',
      trendUp: false
    }
  ];

  return (
    <PageLayout>
      <PageHeader
        title="Program Dashboard"
        description="Monitor field operations and program performance"
        icon={BarChart3}
        actions={[
          {
            label: 'Manage Staff',
            onClick: () => onViewChange?.('staff-management'),
            icon: Users,
            variant: 'default' as const,
            className: 'bg-ilead-green hover:bg-ilead-dark-green text-white btn-mobile'
          },
          {
            label: 'View Reports',
            onClick: () => onViewChange?.('reports'),
            icon: FileText,
            variant: 'outline' as const,
            className: 'btn-mobile'
          }
        ]}
      />

      {/* Key Metrics */}
      <ContentCard noPadding>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
          {keyMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <Card
                key={index}
                className="border-l-4 border-l-ilead-green cursor-pointer hover:shadow-md transition-shadow card-mobile"
                onClick={metric.onClick}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className={`${metric.bgColor} p-2 rounded-lg`}>
                      <Icon className={`h-5 w-5 ${metric.color}`} />
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                    <p className="text-xs text-gray-500">{metric.subtitle}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </ContentCard>

      {/* Performance Overview */}
      <ContentCard title="Performance Overview">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {performanceCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <div key={index} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Icon className="h-5 w-5 text-gray-600" />
                  <Badge variant={card.trendUp ? 'default' : 'secondary'} className="text-xs">
                    {card.trend}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">{card.title}</p>
                <p className="text-xl font-bold text-gray-900">{card.value}</p>
              </div>
            );
          })}
        </div>
      </ContentCard>

      {/* Quick Actions */}
      <ContentCard title="Quick Actions">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="w-full bg-blue-50 hover:bg-blue-100 border-blue-200 btn-mobile"
            onClick={() => onViewChange?.('staff-management')}
          >
            <Users className="h-4 w-4 mr-2" />
            Manage Staff
          </Button>
          <Button
            variant="outline"
            className="w-full bg-green-50 hover:bg-green-100 border-green-200 btn-mobile"
            onClick={() => onViewChange?.('schools')}
          >
            <School className="h-4 w-4 mr-2" />
            School Registry
          </Button>
          <Button
            variant="outline"
            className="w-full bg-orange-50 hover:bg-orange-100 border-orange-200 btn-mobile"
            onClick={() => onViewChange?.('books')}
          >
            <BookOpen className="h-4 w-4 mr-2" />
            Book Management
          </Button>
          <Button
            variant="outline"
            className="w-full bg-purple-50 hover:bg-purple-100 border-purple-200 btn-mobile"
            onClick={() => onViewChange?.('field-visits')}
          >
            <MapPin className="h-4 w-4 mr-2" />
            Field Reports
          </Button>
        </div>
      </ContentCard>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4 bg-gray-100">
          <TabsTrigger value="overview" className="data-[state=active]:bg-ilead-green data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="tasks" className="data-[state=active]:bg-ilead-orange data-[state=active]:text-white">
            Task Management
          </TabsTrigger>
          <TabsTrigger value="activity" className="data-[state=active]:bg-ilead-green data-[state=active]:text-white">
            Recent Activity
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Staff Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2 text-blue-600" />
                  Staff Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Active Field Staff</span>
                    <span className="font-semibold">{metrics?.activeStaff || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Field Visits This Week</span>
                    <span className="font-semibold">{metrics?.fieldVisitsThisWeek || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Task Completion Rate</span>
                    <span className="font-semibold">{metrics?.averageTaskCompletion || 0}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Program Impact */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-green-600" />
                  Program Impact
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Schools Reached</span>
                    <span className="font-semibold">{metrics?.activeSchools || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Books Distributed</span>
                    <span className="font-semibold">{metrics?.totalBooks?.toLocaleString() || '0'}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">This Month Distributions</span>
                    <span className="font-semibold">{metrics?.thisMonthDistributions || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-ilead-green" />
                  Task Management
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onViewChange?.('tasks')}
                  className="text-ilead-green hover:text-ilead-dark-green"
                >
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {tasks.slice(0, 6).map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
                    onClick={() => onViewChange?.('tasks')}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        task.priority === 'urgent' ? 'bg-red-500' :
                        task.priority === 'high' ? 'bg-orange-500' :
                        task.priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                      }`} />
                      <div>
                        <p className="font-medium text-gray-900">{task.title}</p>
                        <p className="text-sm text-gray-600">
                          {task.assigned_to_name ? `Assigned to ${task.assigned_to_name}` : 'Unassigned'}
                        </p>
                      </div>
                    </div>
                    <Badge variant={task.status === 'completed' ? 'default' : 'secondary'}>
                      {task.status}
                    </Badge>
                  </div>
                ))}
                {tasks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No tasks to display</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-ilead-orange" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ActivityFeed
                limit={10}
                showFilters={true}
                enableInfiniteScroll={true}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};
