-- Create general notifications system for role-aware in-app notifications
-- This extends beyond attendance notifications to cover all app activities

-- Create notification types enum
CREATE TYPE notification_type AS ENUM (
    'task_assigned',
    'task_completed',
    'task_overdue',
    'field_report_submitted',
    'field_report_approved',
    'field_report_rejected',
    'book_distribution_created',
    'book_distribution_updated',
    'school_added',
    'school_updated',
    'user_created',
    'user_updated',
    'system_update',
    'weekly_summary',
    'monthly_report',
    'low_inventory',
    'check_in_reminder',
    'check_out_reminder',
    'session_starting',
    'session_completed'
);

-- Create notification priority enum
CREATE TYPE notification_priority AS ENUM ('low', 'normal', 'high', 'urgent');

-- Create notification status enum
CREATE TYPE notification_status AS ENUM ('unread', 'read', 'archived', 'deleted');

-- Create general notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core notification data
    type notification_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority notification_priority DEFAULT 'normal',
    status notification_status DEFAULT 'unread',
    
    -- Recipient information
    recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recipient_role VARCHAR(50) NOT NULL,
    
    -- Sender information (optional for system notifications)
    sender_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    sender_role VARCHAR(50),
    
    -- Related entities (optional)
    related_entity_type VARCHAR(50), -- 'task', 'field_report', 'school', 'book', 'user', etc.
    related_entity_id UUID,
    
    -- Metadata and context
    metadata JSONB DEFAULT '{}',
    action_url VARCHAR(500), -- URL to navigate to when notification is clicked
    
    -- Delivery tracking
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    archived_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Expiry (optional)
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_notifications_recipient_id ON notifications(recipient_id);
CREATE INDEX idx_notifications_recipient_role ON notifications(recipient_role);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_related_entity ON notifications(related_entity_type, related_entity_id);
CREATE INDEX idx_notifications_unread ON notifications(recipient_id, status) WHERE status = 'unread';

-- Enable RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Users can only see their own notifications
CREATE POLICY "Users can view own notifications" ON notifications
    FOR SELECT USING (auth.uid() = recipient_id);

-- Users can update their own notifications (mark as read, archive, etc.)
CREATE POLICY "Users can update own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = recipient_id);

-- System can insert notifications for any user
CREATE POLICY "System can create notifications" ON notifications
    FOR INSERT WITH CHECK (true);

-- Users cannot delete notifications (only archive)
CREATE POLICY "Users cannot delete notifications" ON notifications
    FOR DELETE USING (false);

-- Create notification templates table for reusable notification content
CREATE TABLE IF NOT EXISTS notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type notification_type NOT NULL UNIQUE,
    title_template VARCHAR(255) NOT NULL,
    message_template TEXT NOT NULL,
    priority notification_priority DEFAULT 'normal',
    action_url_template VARCHAR(500),
    metadata_schema JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default notification templates
INSERT INTO notification_templates (type, title_template, message_template, priority, action_url_template) VALUES
('task_assigned', 'New Task Assigned', 'You have been assigned a new task: {task_title}', 'normal', 'tasks'),
('task_completed', 'Task Completed', 'Task "{task_title}" has been marked as completed', 'low', 'tasks'),
('task_overdue', 'Task Overdue', 'Task "{task_title}" is now overdue. Please complete it as soon as possible.', 'high', 'tasks'),
('field_report_submitted', 'Field Report Submitted', 'A new field report has been submitted by {staff_name} for {school_name}', 'normal', 'staff-reports'),
('field_report_approved', 'Field Report Approved', 'Your field report for {school_name} has been approved', 'low', 'field-visits'),
('field_report_rejected', 'Field Report Rejected', 'Your field report for {school_name} needs revision. Reason: {reason}', 'high', 'field-visits'),
('book_distribution_created', 'New Book Distribution', 'Books have been distributed to {school_name}: {quantity} x {book_title}', 'normal', 'books'),
('school_added', 'New School Added', 'A new school has been added: {school_name} in {district}', 'low', 'schools'),
('user_created', 'New User Added', 'A new {role} user has been added: {user_name}', 'low', 'staff-management'),
('system_update', 'System Update', 'System maintenance completed. New features and improvements are now available.', 'normal', 'dashboard'),
('weekly_summary', 'Weekly Summary', 'Your weekly activity summary is ready for review', 'low', 'dashboard'),
('low_inventory', 'Low Inventory Alert', 'Book inventory is running low: {book_title} ({quantity} remaining)', 'high', 'books'),
('check_in_reminder', 'Check-in Reminder', 'Don''t forget to check in at your assigned school today', 'normal', 'field-visits'),
('session_starting', 'Session Starting', 'Your session "{session_name}" at {school_name} is starting in 15 minutes', 'high', 'field-visits');

-- Create function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (
        SELECT COUNT(*)::INTEGER
        FROM notifications
        WHERE recipient_id = user_id
        AND status = 'unread'
        AND (expires_at IS NULL OR expires_at > NOW())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(notification_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notifications
    SET status = 'read', read_at = NOW(), updated_at = NOW()
    WHERE id = notification_id AND recipient_id = user_id AND status = 'unread';
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark all notifications as read
CREATE OR REPLACE FUNCTION mark_all_notifications_read(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE notifications
    SET status = 'read', read_at = NOW(), updated_at = NOW()
    WHERE recipient_id = user_id AND status = 'unread';
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to archive notification
CREATE OR REPLACE FUNCTION archive_notification(notification_id UUID, user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE notifications
    SET status = 'archived', archived_at = NOW(), updated_at = NOW()
    WHERE id = notification_id AND recipient_id = user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean up expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM notifications
    WHERE expires_at IS NOT NULL AND expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notification_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_notification_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_updated_at();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE ON notifications TO authenticated;
GRANT SELECT ON notification_templates TO authenticated;
GRANT EXECUTE ON FUNCTION get_unread_notification_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_notification_read(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_all_notifications_read(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION archive_notification(UUID, UUID) TO authenticated;
