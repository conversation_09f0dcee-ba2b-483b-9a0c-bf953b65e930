-- Create RPC Functions for Data Filtering and Role-Based Access Control
-- This migration creates functions to support proper data filtering for field staff

-- Function to get schools assigned to a specific field staff member
CREATE OR REPLACE FUNCTION get_assigned_schools(p_staff_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    code TEXT,
    district TEXT,
    sub_county TEXT,
    registration_status TEXT,
    head_teacher_name TEXT,
    head_teacher_email TEXT,
    deputy_head_teacher_name TEX<PERSON>,
    deputy_head_teacher_email TEXT,
    champion_teacher_name TEXT,
    champion_teacher_email TEXT,
    champion_teacher_phone TEXT,
    date_joined DATE,
    is_active BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if the requesting user has permission to access this data
    -- Field staff can only see schools they are assigned to
    -- Admin and program officers can see all schools
    
    -- Get the current user's role
    DECLARE
        current_user_role user_role;
        current_user_id UUID;
    BEGIN
        current_user_id := auth.uid();
        
        SELECT role INTO current_user_role
        FROM profiles
        WHERE id = current_user_id;
        
        -- If admin or program officer, return all schools
        IF current_user_role IN ('admin', 'program_officer') THEN
            RETURN QUERY
            SELECT s.id, s.name, s.code, s.district, s.sub_county, s.registration_status,
                   s.head_teacher_name, s.head_teacher_email, s.deputy_head_teacher_name,
                   s.deputy_head_teacher_email, s.champion_teacher_name, s.champion_teacher_email,
                   s.champion_teacher_phone, s.date_joined, s.is_active, s.created_at, s.updated_at
            FROM schools s
            ORDER BY s.name;
            RETURN;
        END IF;
        
        -- If field staff, only return schools they are assigned to through tasks
        IF current_user_role = 'field_staff' THEN
            -- Only allow access to own data
            IF p_staff_id != current_user_id THEN
                RAISE EXCEPTION 'Access denied: Field staff can only access their own assigned schools';
            END IF;
            
            RETURN QUERY
            SELECT DISTINCT s.id, s.name, s.code, s.district, s.sub_county, s.registration_status,
                   s.head_teacher_name, s.head_teacher_email, s.deputy_head_teacher_name,
                   s.deputy_head_teacher_email, s.champion_teacher_name, s.champion_teacher_email,
                   s.champion_teacher_phone, s.date_joined, s.is_active, s.created_at, s.updated_at
            FROM schools s
            INNER JOIN tasks t ON t.school_id = s.id
            WHERE t.assigned_to = p_staff_id
            ORDER BY s.name;
            RETURN;
        END IF;
        
        -- If no valid role, return empty result
        RETURN;
    END;
END;
$$;

-- Function to get field staff attendance with proper filtering
CREATE OR REPLACE FUNCTION get_filtered_field_staff_attendance(
    p_staff_id UUID DEFAULT NULL,
    p_date_from DATE DEFAULT NULL,
    p_date_to DATE DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    staff_id UUID,
    school_id UUID,
    attendance_date DATE,
    check_in_time TIMESTAMPTZ,
    check_out_time TIMESTAMPTZ,
    status TEXT,
    total_duration_minutes INTEGER,
    location_lat DECIMAL,
    location_lng DECIMAL,
    location_accuracy DECIMAL,
    notes TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    staff_name TEXT,
    school_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_role user_role;
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    -- Admin and program officers can see all attendance data
    IF current_user_role IN ('admin', 'program_officer') THEN
        RETURN QUERY
        SELECT fsa.id, fsa.staff_id, fsa.school_id, fsa.attendance_date,
               fsa.check_in_time, fsa.check_out_time, fsa.status,
               fsa.total_duration_minutes, fsa.location_lat, fsa.location_lng,
               fsa.location_accuracy, fsa.notes, fsa.created_at, fsa.updated_at,
               p.name as staff_name, s.name as school_name
        FROM field_staff_attendance fsa
        LEFT JOIN profiles p ON fsa.staff_id = p.id
        LEFT JOIN schools s ON fsa.school_id = s.id
        WHERE (p_staff_id IS NULL OR fsa.staff_id = p_staff_id)
          AND (p_date_from IS NULL OR fsa.attendance_date >= p_date_from)
          AND (p_date_to IS NULL OR fsa.attendance_date <= p_date_to)
        ORDER BY fsa.attendance_date DESC, fsa.check_in_time DESC;
        RETURN;
    END IF;
    
    -- Field staff can only see their own attendance data
    IF current_user_role = 'field_staff' THEN
        -- Ensure field staff can only access their own data
        IF p_staff_id IS NOT NULL AND p_staff_id != current_user_id THEN
            RAISE EXCEPTION 'Access denied: Field staff can only access their own attendance data';
        END IF;
        
        RETURN QUERY
        SELECT fsa.id, fsa.staff_id, fsa.school_id, fsa.attendance_date,
               fsa.check_in_time, fsa.check_out_time, fsa.status,
               fsa.total_duration_minutes, fsa.location_lat, fsa.location_lng,
               fsa.location_accuracy, fsa.notes, fsa.created_at, fsa.updated_at,
               p.name as staff_name, s.name as school_name
        FROM field_staff_attendance fsa
        LEFT JOIN profiles p ON fsa.staff_id = p.id
        LEFT JOIN schools s ON fsa.school_id = s.id
        WHERE fsa.staff_id = current_user_id
          AND (p_date_from IS NULL OR fsa.attendance_date >= p_date_from)
          AND (p_date_to IS NULL OR fsa.attendance_date <= p_date_to)
        ORDER BY fsa.attendance_date DESC, fsa.check_in_time DESC;
        RETURN;
    END IF;
    
    -- If no valid role, return empty result
    RETURN;
END;
$$;

-- Function to get filtered field reports
CREATE OR REPLACE FUNCTION get_filtered_field_reports(
    p_staff_id UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL,
    p_date_from DATE DEFAULT NULL,
    p_date_to DATE DEFAULT NULL,
    p_limit INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    staff_id UUID,
    school_id UUID,
    attendance_id UUID,
    report_date DATE,
    activity_type activity_report_type,
    round_table_sessions_count INTEGER,
    total_students_attended INTEGER,
    students_per_session INTEGER,
    activities_conducted TEXT,
    topics_covered TEXT,
    challenges_encountered TEXT,
    wins_achieved TEXT,
    general_observations TEXT,
    lessons_learned TEXT,
    follow_up_required BOOLEAN,
    follow_up_actions TEXT,
    introduction TEXT,
    recommendations TEXT,
    status report_status,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    staff_name TEXT,
    school_name TEXT,
    check_in_time TIMESTAMPTZ,
    check_out_time TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_role user_role;
    current_user_id UUID;
BEGIN
    current_user_id := auth.uid();
    
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    -- Admin and program officers can see all field reports
    IF current_user_role IN ('admin', 'program_officer') THEN
        RETURN QUERY
        SELECT fr.id, fr.staff_id, fr.school_id, fr.attendance_id, fr.report_date,
               fr.activity_type, fr.round_table_sessions_count, fr.total_students_attended,
               fr.students_per_session, fr.activities_conducted, fr.topics_covered,
               fr.challenges_encountered, fr.wins_achieved, fr.general_observations,
               fr.lessons_learned, fr.follow_up_required, fr.follow_up_actions,
               fr.introduction, fr.recommendations, fr.status, fr.created_at, fr.updated_at,
               p.name as staff_name, s.name as school_name,
               fsa.check_in_time, fsa.check_out_time
        FROM field_reports fr
        LEFT JOIN profiles p ON fr.staff_id = p.id
        LEFT JOIN schools s ON fr.school_id = s.id
        LEFT JOIN field_staff_attendance fsa ON fr.attendance_id = fsa.id
        WHERE (p_staff_id IS NULL OR fr.staff_id = p_staff_id)
          AND (p_school_id IS NULL OR fr.school_id = p_school_id)
          AND (p_date_from IS NULL OR fr.report_date >= p_date_from)
          AND (p_date_to IS NULL OR fr.report_date <= p_date_to)
        ORDER BY fr.report_date DESC, fr.created_at DESC
        LIMIT p_limit;
        RETURN;
    END IF;
    
    -- Field staff can only see their own field reports
    IF current_user_role = 'field_staff' THEN
        -- Ensure field staff can only access their own data
        IF p_staff_id IS NOT NULL AND p_staff_id != current_user_id THEN
            RAISE EXCEPTION 'Access denied: Field staff can only access their own field reports';
        END IF;
        
        RETURN QUERY
        SELECT fr.id, fr.staff_id, fr.school_id, fr.attendance_id, fr.report_date,
               fr.activity_type, fr.round_table_sessions_count, fr.total_students_attended,
               fr.students_per_session, fr.activities_conducted, fr.topics_covered,
               fr.challenges_encountered, fr.wins_achieved, fr.general_observations,
               fr.lessons_learned, fr.follow_up_required, fr.follow_up_actions,
               fr.introduction, fr.recommendations, fr.status, fr.created_at, fr.updated_at,
               p.name as staff_name, s.name as school_name,
               fsa.check_in_time, fsa.check_out_time
        FROM field_reports fr
        LEFT JOIN profiles p ON fr.staff_id = p.id
        LEFT JOIN schools s ON fr.school_id = s.id
        LEFT JOIN field_staff_attendance fsa ON fr.attendance_id = fsa.id
        WHERE fr.staff_id = current_user_id
          AND (p_school_id IS NULL OR fr.school_id = p_school_id)
          AND (p_date_from IS NULL OR fr.report_date >= p_date_from)
          AND (p_date_to IS NULL OR fr.report_date <= p_date_to)
        ORDER BY fr.report_date DESC, fr.created_at DESC
        LIMIT p_limit;
        RETURN;
    END IF;
    
    -- If no valid role, return empty result
    RETURN;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_assigned_schools(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_filtered_field_staff_attendance(UUID, DATE, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_filtered_field_reports(UUID, UUID, DATE, DATE, INTEGER) TO authenticated;
