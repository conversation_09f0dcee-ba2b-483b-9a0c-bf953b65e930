import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Bell,
  Check<PERSON>heck,
  Clock,
  ExternalLink,
  Settings,
  Archive,
  AlertTriangle,
  CheckSquare,
  FileText,
  BookOpen,
  School,
  User,
  Package,
  Info
} from 'lucide-react';
import { 
  Notification, 
  NotificationType,
  useMarkNotificationRead, 
  useMarkAllNotificationsRead,
  useArchiveNotification 
} from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';

interface NotificationDropdownProps {
  notifications: Notification[];
  unreadCount: number;
  onNavigate?: (route: string) => void;
  onClose?: () => void;
}

const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'task_assigned':
    case 'task_completed':
    case 'task_overdue':
      return CheckSquare;
    case 'field_report_submitted':
    case 'field_report_approved':
    case 'field_report_rejected':
      return FileText;
    case 'book_distribution_created':
    case 'book_distribution_updated':
    case 'low_inventory':
      return BookOpen;
    case 'school_added':
    case 'school_updated':
      return School;
    case 'user_created':
    case 'user_updated':
      return User;
    case 'system_update':
    case 'weekly_summary':
    case 'monthly_report':
      return Info;
    case 'check_in_reminder':
    case 'check_out_reminder':
    case 'session_starting':
    case 'session_completed':
      return Clock;
    default:
      return Bell;
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'urgent':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'high':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'normal':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'low':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
  unreadCount,
  onNavigate,
  onClose
}) => {
  const markAsRead = useMarkNotificationRead();
  const markAllAsRead = useMarkAllNotificationsRead();
  const archiveNotification = useArchiveNotification();

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read if unread
    if (notification.status === 'unread') {
      markAsRead.mutate(notification.id);
    }

    // Navigate if action URL is provided
    if (notification.action_url && onNavigate) {
      onNavigate(notification.action_url);
      onClose?.();
    }
  };

  const handleMarkAllRead = () => {
    if (unreadCount > 0) {
      markAllAsRead.mutate();
    }
  };

  const handleArchive = (e: React.MouseEvent, notificationId: string) => {
    e.stopPropagation();
    archiveNotification.mutate(notificationId);
  };

  if (notifications.length === 0) {
    return (
      <div className="p-6 text-center">
        <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications</h3>
        <p className="text-sm text-gray-600">
          You're all caught up! New notifications will appear here.
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-gray-600" />
            <h3 className="font-semibold text-gray-900">Notifications</h3>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {unreadCount} new
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllRead}
                disabled={markAllAsRead.isPending}
                className="text-xs"
              >
                <CheckCheck className="h-3 w-3 mr-1" />
                Mark all read
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onNavigate?.('settings');
                onClose?.();
              }}
              className="text-xs"
            >
              <Settings className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <ScrollArea className="max-h-96">
        <div className="divide-y">
          {notifications.map((notification) => {
            const Icon = getNotificationIcon(notification.type);
            const isUnread = notification.status === 'unread';
            const timeAgo = formatDistanceToNow(new Date(notification.created_at), { addSuffix: true });

            return (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  isUnread ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start gap-3">
                  {/* Icon */}
                  <div className={`p-2 rounded-lg ${isUnread ? 'bg-blue-100' : 'bg-gray-100'}`}>
                    <Icon className={`h-4 w-4 ${isUnread ? 'text-blue-600' : 'text-gray-600'}`} />
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-1">
                      <h4 className={`text-sm font-medium truncate ${
                        isUnread ? 'text-gray-900' : 'text-gray-700'
                      }`}>
                        {notification.title}
                      </h4>
                      
                      <div className="flex items-center gap-1 ml-2">
                        {isUnread && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full" />
                        )}
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => handleArchive(e, notification.id)}
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-gray-200"
                        >
                          <Archive className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {notification.message}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPriorityColor(notification.priority)}`}
                        >
                          {notification.priority}
                        </Badge>
                        
                        <span className="text-xs text-gray-500">
                          {timeAgo}
                        </span>
                      </div>

                      {notification.action_url && (
                        <ExternalLink className="h-3 w-3 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>

      {/* Footer */}
      {notifications.length >= 10 && (
        <>
          <Separator />
          <div className="p-3 text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                onNavigate?.('notifications');
                onClose?.();
              }}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              View all notifications
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationDropdown;
