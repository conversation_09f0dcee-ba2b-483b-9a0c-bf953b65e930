import React, { useState } from 'react';
import { Toaster } from '@/components/ui/toaster';
import NavigationSimplified from '@/components/NavigationSimplified';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';
import { AccessControl, AdminOnly, AdminProgramOfficer } from '@/components/common/AccessControl';
import { 
  getComponentForRoute, 
  legacyRouteRedirects,
  canAccessRoute as configCanAccessRoute 
} from '@/config/navigation';

// Import components
import Dashboard from '@/components/Dashboard';
import SchoolList from '@/components/schools/SchoolList';
import UnifiedTaskManagement from '@/components/tasks/UnifiedTaskManagement';
import UnifiedFieldVisits from '@/components/field-visits/UnifiedFieldVisits';
import Books from '@/components/Books';
import StaffManagement from '@/components/staff/StaffManagement';
import ComprehensiveReports from '@/components/reports/ComprehensiveReports';
import ImpactOverview from '@/components/impact/ImpactOverview';
import StudentOutcomes from '@/components/impact/student-outcomes/StudentOutcomes';
import BeneficiaryFeedbackModule from '@/components/impact/beneficiary-feedback/BeneficiaryFeedbackModule';
import Settings from '@/components/Settings';
import Help from '@/components/Help';

const AuthenticatedAppSimplified = () => {
  const { user, profile } = useAuth();
  const { currentUser, canAccessRoute } = useAccessControl();
  const [currentView, setCurrentView] = useState('dashboard');

  // Handle view changes with access control
  const handleViewChange = (view: string) => {
    // Check for legacy redirects
    const finalView = legacyRouteRedirects[view] || view;
    
    // Check access
    if (currentUser && configCanAccessRoute(currentUser.role, finalView)) {
      setCurrentView(finalView);
    } else {
      console.warn(`Access denied to route: ${finalView}`);
      // Optionally redirect to dashboard or show error
      setCurrentView('dashboard');
    }
  };

  // Render main content based on current view
  const renderMainContent = () => {
    // Get the final route (handle redirects)
    const route = legacyRouteRedirects[currentView] || currentView;
    
    switch (route) {
      case 'dashboard':
        return <Dashboard onViewChange={handleViewChange} />;
      
      case 'schools':
        return <SchoolList />;
      
      case 'tasks':
        return <UnifiedTaskManagement />;
      
      case 'field-visits':
        return <UnifiedFieldVisits />;
      
      case 'books':
        return (
          <AdminProgramOfficer fallbackMessage="Access to book management is restricted to administrators and program officers only.">
            <Books />
          </AdminProgramOfficer>
        );
      
      case 'staff-management':
        return (
          <AdminProgramOfficer fallbackMessage="Access to staff management is restricted to administrators and program officers only.">
            <StaffManagement />
          </AdminProgramOfficer>
        );
      
      case 'impact':
        return (
          <AdminOnly fallbackMessage="Access to impact measurement is restricted to administrators only.">
            <ImpactOverview />
          </AdminOnly>
        );
      
      case 'impact-students':
        return (
          <AdminOnly fallbackMessage="Access to student outcomes is restricted to administrators only.">
            <StudentOutcomes />
          </AdminOnly>
        );
      
      case 'impact-feedback':
        return (
          <AdminOnly fallbackMessage="Access to beneficiary feedback is restricted to administrators only.">
            <BeneficiaryFeedbackModule />
          </AdminOnly>
        );
      
      case 'impact-reports':
        return (
          <AdminOnly fallbackMessage="Access to impact reports is restricted to administrators only.">
            <ComprehensiveReports />
          </AdminOnly>
        );
      
      case 'settings':
        return (
          <AdminProgramOfficer fallbackMessage="Access to settings is restricted to administrators and program officers only.">
            <Settings />
          </AdminProgramOfficer>
        );
      
      case 'help':
        return <Help />;
      
      case 'help-docs':
        return (
          <AdminOnly fallbackMessage="Access to documentation is restricted to administrators only.">
            <div className="p-6">
              <h1 className="text-2xl font-bold mb-4">Documentation</h1>
              <p>System documentation and guides will be available here.</p>
            </div>
          </AdminOnly>
        );
      
      default:
        return (
          <div className="p-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Page Not Found</h2>
              <p className="text-gray-600 mb-4">The requested page could not be found.</p>
              <button
                onClick={() => handleViewChange('dashboard')}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        );
    }
  };

  if (!user || !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex-shrink-0">
        <NavigationSimplified
          currentUser={profile}
          currentView={currentView}
          onViewChange={handleViewChange}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <Header 
          currentUser={profile}
          onViewChange={handleViewChange}
        />

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto">
          {renderMainContent()}
        </main>

        {/* Footer */}
        <Footer />
      </div>

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
};

export default AuthenticatedAppSimplified;
