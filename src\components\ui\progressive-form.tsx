import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  Check, 
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FormStep {
  id: string;
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  required?: boolean;
  validation?: () => boolean | Promise<boolean>;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  condition?: () => boolean; // Conditional step visibility
}

export interface ProgressiveFormProps {
  steps: FormStep[];
  onComplete: (data: any) => void | Promise<void>;
  onCancel?: () => void;
  initialData?: any;
  className?: string;
  showProgress?: boolean;
  showStepNumbers?: boolean;
  allowSkipOptional?: boolean;
  showStepPreview?: boolean;
  mobileOptimized?: boolean;
  title?: string;
  description?: string;
}

export interface ProgressiveFormContextValue {
  currentStep: number;
  totalSteps: number;
  data: any;
  updateData: (stepData: any) => void;
  goToStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  canProceed: boolean;
  isCompleting: boolean;
}

const ProgressiveFormContext = React.createContext<ProgressiveFormContextValue | null>(null);

export const useProgressiveForm = () => {
  const context = React.useContext(ProgressiveFormContext);
  if (!context) {
    throw new Error('useProgressiveForm must be used within a ProgressiveForm');
  }
  return context;
};

export const ProgressiveForm: React.FC<ProgressiveFormProps> = ({
  steps,
  onComplete,
  onCancel,
  initialData = {},
  className,
  showProgress = true,
  showStepNumbers = true,
  allowSkipOptional = false,
  showStepPreview = false,
  mobileOptimized = true,
  title,
  description
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [data, setData] = useState(initialData);
  const [stepValidation, setStepValidation] = useState<Record<number, boolean>>({});
  const [isCompleting, setIsCompleting] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Filter steps based on conditions
  const visibleSteps = useMemo(() => {
    return steps.filter(step => !step.condition || step.condition());
  }, [steps, data]);

  const totalSteps = visibleSteps.length;
  const currentStepData = visibleSteps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  // Calculate progress percentage
  const progressPercentage = totalSteps > 0 ? ((currentStep + 1) / totalSteps) * 100 : 0;

  // Check if current step is valid
  const canProceed = useMemo(() => {
    if (!currentStepData) return false;
    if (!currentStepData.required) return true;
    return stepValidation[currentStep] !== false;
  }, [currentStep, currentStepData, stepValidation]);

  // Update form data
  const updateData = useCallback((stepData: any) => {
    setData(prev => ({ ...prev, ...stepData }));
  }, []);

  // Validate current step
  const validateCurrentStep = useCallback(async () => {
    if (!currentStepData?.validation) return true;
    
    try {
      const isValid = await currentStepData.validation();
      setStepValidation(prev => ({ ...prev, [currentStep]: isValid }));
      return isValid;
    } catch (error) {
      setStepValidation(prev => ({ ...prev, [currentStep]: false }));
      return false;
    }
  }, [currentStep, currentStepData]);

  // Navigation functions
  const goToStep = useCallback((step: number) => {
    if (step >= 0 && step < totalSteps) {
      setCurrentStep(step);
    }
  }, [totalSteps]);

  const nextStep = useCallback(async () => {
    if (isLastStep) return;
    
    const isValid = await validateCurrentStep();
    if (isValid || (allowSkipOptional && !currentStepData?.required)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps - 1));
    }
  }, [isLastStep, validateCurrentStep, allowSkipOptional, currentStepData, totalSteps]);

  const previousStep = useCallback(() => {
    if (!isFirstStep) {
      setCurrentStep(prev => Math.max(prev - 1, 0));
    }
  }, [isFirstStep]);

  // Complete form
  const handleComplete = useCallback(async () => {
    setIsCompleting(true);
    try {
      const isValid = await validateCurrentStep();
      if (isValid || (allowSkipOptional && !currentStepData?.required)) {
        await onComplete(data);
      }
    } catch (error) {
      console.error('Form completion error:', error);
    } finally {
      setIsCompleting(false);
    }
  }, [validateCurrentStep, allowSkipOptional, currentStepData, onComplete, data]);

  // Context value
  const contextValue: ProgressiveFormContextValue = {
    currentStep,
    totalSteps,
    data,
    updateData,
    goToStep,
    nextStep,
    previousStep,
    isFirstStep,
    isLastStep,
    canProceed,
    isCompleting
  };

  // Step indicator component
  const StepIndicator = () => (
    <div className="flex items-center justify-between mb-6">
      {visibleSteps.map((step, index) => {
        const isActive = index === currentStep;
        const isCompleted = index < currentStep;
        const isValid = stepValidation[index] !== false;
        
        return (
          <div key={step.id} className="flex items-center">
            <div
              className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors cursor-pointer",
                isActive && "bg-ilead-green text-white ring-2 ring-ilead-green ring-offset-2",
                isCompleted && "bg-ilead-green text-white",
                !isActive && !isCompleted && "bg-gray-200 text-gray-600",
                !isValid && index <= currentStep && "bg-red-500 text-white"
              )}
              onClick={() => goToStep(index)}
            >
              {isCompleted ? (
                <Check className="h-4 w-4" />
              ) : !isValid && index <= currentStep ? (
                <AlertCircle className="h-3 w-3" />
              ) : showStepNumbers ? (
                index + 1
              ) : (
                step.icon && <step.icon className="h-4 w-4" />
              )}
            </div>
            {index < totalSteps - 1 && (
              <div className={cn(
                "h-px w-8 mx-2 transition-colors",
                index < currentStep ? "bg-ilead-green" : "bg-gray-200"
              )} />
            )}
          </div>
        );
      })}
    </div>
  );

  // Mobile step indicator (simplified)
  const MobileStepIndicator = () => (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <Badge variant="outline" className="text-xs">
          Step {currentStep + 1} of {totalSteps}
        </Badge>
        {currentStepData?.required && (
          <Badge variant="secondary" className="text-xs">Required</Badge>
        )}
      </div>
      {showStepPreview && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowPreview(!showPreview)}
          className="text-xs"
        >
          {showPreview ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          Preview
        </Button>
      )}
    </div>
  );

  if (!currentStepData) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">No steps available</p>
        </CardContent>
      </Card>
    );
  }

  const StepComponent = currentStepData.component;

  return (
    <ProgressiveFormContext.Provider value={contextValue}>
      <Card className={cn("w-full max-w-4xl mx-auto", className)}>
        {(title || description) && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <p className="text-gray-600">{description}</p>}
          </CardHeader>
        )}
        
        <CardContent className={cn("p-6", mobileOptimized && "p-4 sm:p-6")}>
          {/* Progress bar */}
          {showProgress && (
            <div className="mb-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>{currentStepData.title}</span>
                <span>{Math.round(progressPercentage)}% complete</span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>
          )}

          {/* Step indicators */}
          {mobileOptimized ? (
            <div className="block sm:hidden">
              <MobileStepIndicator />
            </div>
          ) : null}
          
          <div className={cn(mobileOptimized && "hidden sm:block")}>
            <StepIndicator />
          </div>

          {/* Step preview */}
          {showPreview && showStepPreview && (
            <Card className="mb-4 bg-gray-50">
              <CardContent className="p-4">
                <h4 className="font-medium mb-2">Form Preview</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  {visibleSteps.map((step, index) => (
                    <div key={step.id} className="flex justify-between">
                      <span>{step.title}</span>
                      <span className={cn(
                        index < currentStep ? "text-green-600" : 
                        index === currentStep ? "text-blue-600" : "text-gray-400"
                      )}>
                        {index < currentStep ? "✓" : index === currentStep ? "→" : "○"}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Current step content */}
          <div className="mb-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {currentStepData.title}
              </h3>
              {currentStepData.description && (
                <p className="text-sm text-gray-600 mt-1">
                  {currentStepData.description}
                </p>
              )}
            </div>
            
            <StepComponent 
              {...(currentStepData.props || {})}
              data={data}
              updateData={updateData}
            />
          </div>

          {/* Navigation buttons */}
          <div className={cn(
            "flex justify-between",
            mobileOptimized && "flex-col-reverse sm:flex-row gap-3 sm:gap-0"
          )}>
            <div className="flex space-x-2">
              {!isFirstStep && (
                <Button
                  variant="outline"
                  onClick={previousStep}
                  className={cn(mobileOptimized && "btn-mobile")}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
              )}
              {onCancel && (
                <Button
                  variant="ghost"
                  onClick={onCancel}
                  className={cn(mobileOptimized && "btn-mobile")}
                >
                  Cancel
                </Button>
              )}
            </div>

            <div className="flex space-x-2">
              {allowSkipOptional && !currentStepData.required && !isLastStep && (
                <Button
                  variant="outline"
                  onClick={nextStep}
                  className={cn(mobileOptimized && "btn-mobile")}
                >
                  Skip
                </Button>
              )}
              
              {isLastStep ? (
                <Button
                  onClick={handleComplete}
                  disabled={!canProceed || isCompleting}
                  className={cn(
                    "bg-ilead-green hover:bg-ilead-dark-green text-white",
                    mobileOptimized && "btn-mobile"
                  )}
                >
                  {isCompleting ? "Completing..." : "Complete"}
                </Button>
              ) : (
                <Button
                  onClick={nextStep}
                  disabled={!canProceed}
                  className={cn(
                    "bg-ilead-green hover:bg-ilead-dark-green text-white",
                    mobileOptimized && "btn-mobile"
                  )}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </ProgressiveFormContext.Provider>
  );
};
