
import { useState } from 'react';
import { Users, Plus } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';
import { useToast } from '@/hooks/use-toast';
import TaskList from '../TaskList';
import CreateTaskDialog from '../CreateTaskDialog';
import { useManagedTasks, useCreateTask, TaskFormData } from '@/hooks/tasks';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';



const ManagedTasks = () => {
  const { profile } = useAuth();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const { toast } = useToast();

  // Only program officers and admins should access this
  const { roleChecker } = useAccessControl();
  const canManageTasks = roleChecker.canManageTasks();
  const canAssignTasks = canManageTasks;

  // Use optimized hooks
  const { data: managedTasks = [], isLoading } = useManagedTasks();
  const createTaskMutation = useCreateTask();

  const handleCreateTask = async (taskData: TaskFormData) => {
    try {
      await createTaskMutation.mutateAsync(taskData);
      toast({
        title: "Success",
        description: "Task created successfully",
      });
      setCreateDialogOpen(false);
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error).message || "Failed to create task",
        variant: "destructive",
      });
    }
  };

  const handleViewDetails = (taskId: string) => {
    // This would open task details - for now just log
    console.log('View task details:', taskId);
  };

  const handleUpdateStatus = (taskId: string, status: Database['public']['Enums']['task_status']) => {
    // This would update task status - for now just log
    console.log('Update task status:', taskId, status);
  };

  // Loading state while profile is being fetched
  if (!profile || !profile.role) {
    return (
      <div className="p-6 scrollbar-content">
        <div className="max-w-2xl mx-auto">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-blue-800 mb-2">
              Loading Profile
            </h3>
            <p className="text-blue-700">
              Please wait while we verify your permissions...
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Access control check - only after profile is fully loaded
  if (!canManageTasks) {
    return (
      <div className="p-6 scrollbar-content">
        <div className="max-w-2xl mx-auto">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <Users className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">
              Access Restricted
            </h3>
            <p className="text-yellow-700 mb-4">
              Only administrators and program officers can access managed tasks.
            </p>
            <div className="text-sm text-yellow-600">
              <p><strong>Your Role:</strong> {profile.role}</p>
              <p><strong>Required Roles:</strong> Admin, Program Officer</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title={profile?.role === 'admin' ? 'All Tasks' : 'Managed Tasks'}
        description={
          profile?.role === 'admin'
            ? `All tasks in the system (${managedTasks.length} total)`
            : `Tasks you manage and assign (${managedTasks.length} total)`
        }
        icon={Users}
        actions={canAssignTasks ? [
          {
            label: 'New Task',
            onClick: () => setCreateDialogOpen(true),
            icon: Plus,
          }
        ] : []}
      />

      <ContentCard noPadding>
        <TaskList
          tasks={managedTasks}
          loading={isLoading}
          currentUserId={profile?.id}
          onViewDetails={handleViewDetails}
          onUpdateStatus={handleUpdateStatus}
        />
      </ContentCard>

      {/* Create Task Dialog */}
      <CreateTaskDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateTask}
        loading={createTaskMutation.isPending}
        canAssignTasks={canAssignTasks}
      />
    </PageLayout>
  );
};

export default ManagedTasks;
