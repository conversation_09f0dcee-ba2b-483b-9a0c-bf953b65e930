import { useMemo } from 'react';
import { useAuth } from '@/hooks/useAuth';
import {
  checkAccess,
  canAccessRoute,
  canAccessFeature,
  hasPermission,
  hasRoleLevel,
  ACCESS_CONFIGS,
  <PERSON><PERSON><PERSON><PERSON>,
  type AccessControlConfig,
  type UserRole,
  type User
} from '@/utils/rbac';

/**
 * Centralized access control hook
 */
export function useAccessControl() {
  const { user, profile } = useAuth();

  const currentUser: User | null = useMemo(() => {
    if (!user || !profile?.role) return null;
    return {
      id: user.id,
      role: profile.role as UserRole,
    };
  }, [user, profile]);

  /**
   * Check access based on configuration
   */
  const checkUserAccess = useMemo(() => {
    return (config: AccessControlConfig) => {
      return checkAccess(currentUser, config);
    };
  }, [currentUser]);

  /**
   * Check if user can access a specific route
   */
  const canUserAccessRoute = useMemo(() => {
    return (route: string) => {
      if (!currentUser) return false;
      return canAccessRoute(currentUser.role, route);
    };
  }, [currentUser]);

  /**
   * Check if user can access a specific feature
   */
  const canUserAccessFeature = useMemo(() => {
    return (feature: string) => {
      if (!currentUser) return false;
      return canAccessFeature(currentUser.role, feature);
    };
  }, [currentUser]);

  /**
   * Check if user has a specific permission
   */
  const userHasPermission = useMemo(() => {
    return (permission: string) => {
      if (!currentUser) return false;
      return hasPermission(currentUser.role, permission);
    };
  }, [currentUser]);

  /**
   * Check if user has sufficient role level
   */
  const userHasRoleLevel = useMemo(() => {
    return (requiredLevel: UserRole) => {
      if (!currentUser) return false;
      return hasRoleLevel(currentUser.role, requiredLevel);
    };
  }, [currentUser]);

  /**
   * Predefined access checks for common scenarios using centralized role checker
   */
  const isAdmin = RoleChecker.isAdmin(currentUser?.role);
  const isProgramOfficer = RoleChecker.isProgramOfficer(currentUser?.role);
  const isFieldStaff = RoleChecker.isFieldStaff(currentUser?.role);
  const isAdminOrProgramOfficer = RoleChecker.isAdminOrProgramOfficer(currentUser?.role);

  /**
   * Centralized role checking utilities
   */
  const roleChecker = useMemo(() => ({
    isAdmin: () => RoleChecker.isAdmin(currentUser?.role),
    isProgramOfficer: () => RoleChecker.isProgramOfficer(currentUser?.role),
    isFieldStaff: () => RoleChecker.isFieldStaff(currentUser?.role),
    isAdminOrProgramOfficer: () => RoleChecker.isAdminOrProgramOfficer(currentUser?.role),
    canManageUsers: () => RoleChecker.canManageUsers(currentUser?.role),
    canViewAllStaffData: () => RoleChecker.canViewAllStaffData(currentUser?.role),
    canManageTasks: () => RoleChecker.canManageTasks(currentUser?.role),
    canCreateTasks: () => RoleChecker.canCreateTasks(currentUser?.role),
    canManageSchools: () => RoleChecker.canManageSchools(currentUser?.role),
    canExportData: () => RoleChecker.canExportData(currentUser?.role),
    canAccessReports: () => RoleChecker.canAccessReports(currentUser?.role),
    canApproveReports: () => RoleChecker.canApproveReports(currentUser?.role),
    canManageTimesheets: () => RoleChecker.canManageTimesheets(currentUser?.role),
    canCheckInOut: () => RoleChecker.canCheckInOut(currentUser?.role),
    canAccessImpactMeasurement: () => RoleChecker.canAccessImpactMeasurement(currentUser?.role),
    canAccessDocumentation: () => RoleChecker.canAccessDocumentation(currentUser?.role),
    canAccessProfileSettings: () => RoleChecker.canAccessProfileSettings(currentUser?.role),
    canAccessAdminSettings: () => RoleChecker.canAccessAdminSettings(currentUser?.role),
    canViewBooks: () => RoleChecker.canViewBooks(currentUser?.role),
    canManageBooks: () => RoleChecker.canManageBooks(currentUser?.role),
  }), [currentUser]);

  /**
   * Quick access to common configurations
   */
  const accessConfigs = ACCESS_CONFIGS;

  return {
    // User info
    currentUser,
    isAdmin,
    isProgramOfficer,
    isFieldStaff,
    isAdminOrProgramOfficer,

    // Access check functions
    checkAccess: checkUserAccess,
    canAccessRoute: canUserAccessRoute,
    canAccessFeature: canUserAccessFeature,
    hasPermission: userHasPermission,
    hasRoleLevel: userHasRoleLevel,

    // Centralized role checker
    roleChecker,

    // Predefined configs
    accessConfigs,
  };
}

/**
 * Hook for checking access to specific resources with ownership
 */
export function useResourceAccess(resourceOwnerId?: string) {
  const { checkAccess, currentUser } = useAccessControl();

  const canViewResource = useMemo(() => {
    return checkAccess({
      ...ACCESS_CONFIGS.FIELD_STAFF_OWN_DATA,
      targetUserId: resourceOwnerId,
    });
  }, [checkAccess, resourceOwnerId]);

  const canEditResource = useMemo(() => {
    return checkAccess({
      ...ACCESS_CONFIGS.FIELD_STAFF_OWN_DATA,
      targetUserId: resourceOwnerId,
    });
  }, [checkAccess, resourceOwnerId]);

  const canDeleteResource = useMemo(() => {
    return checkAccess({
      requiredRoles: ['admin', 'program_officer'],
      targetUserId: resourceOwnerId,
      allowSelfAccess: true,
    });
  }, [checkAccess, resourceOwnerId]);

  return {
    canView: canViewResource.hasAccess,
    canEdit: canEditResource.hasAccess,
    canDelete: canDeleteResource.hasAccess,
    isOwner: canViewResource.isOwner,
    accessReason: canViewResource.reason,
  };
}

/**
 * Hook for navigation access control
 */
export function useNavigationAccess() {
  const { canAccessRoute, currentUser } = useAccessControl();

  const allowedRoutes = useMemo(() => {
    if (!currentUser) return [];
    
    const routes = [
      'dashboard',
      'field-visits',
      'tasks',
      'schools',
      'books',
      'impact',
      'staff-management',
      'settings',
      'reports',
    ];

    return routes.filter(route => canAccessRoute(route));
  }, [canAccessRoute, currentUser]);

  return {
    allowedRoutes,
    canAccessRoute,
  };
}
