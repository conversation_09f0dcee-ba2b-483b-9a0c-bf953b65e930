import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  ChevronDown, 
  ChevronRight, 
  Eye, 
  EyeOff, 
  Info,
  AlertCircle,
  CheckCircle,
  Plus,
  Minus
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  required?: boolean;
  condition?: (data: any) => boolean;
  children: React.ReactNode;
  defaultOpen?: boolean;
  collapsible?: boolean;
  badge?: string;
  variant?: 'default' | 'outlined' | 'minimal';
}

export interface FormDisclosureProps {
  sections: FormSection[];
  data?: any;
  className?: string;
  allowMultipleOpen?: boolean;
  showProgress?: boolean;
  mobileOptimized?: boolean;
  onSectionToggle?: (sectionId: string, isOpen: boolean) => void;
}

export interface ConditionalFieldProps {
  condition: (data: any) => boolean;
  data: any;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  animateEntry?: boolean;
}

export interface FieldGroupProps {
  title?: string;
  description?: string;
  required?: boolean;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'card' | 'bordered';
  collapsible?: boolean;
  defaultOpen?: boolean;
  icon?: React.ComponentType<{ className?: string }>;
}

// Conditional field component for showing/hiding fields based on data
export const ConditionalField: React.FC<ConditionalFieldProps> = ({
  condition,
  data,
  children,
  fallback,
  className,
  animateEntry = true
}) => {
  const [shouldShow, setShouldShow] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  useEffect(() => {
    const show = condition(data);
    setShouldShow(show);
    if (show && !hasShown) {
      setHasShown(true);
    }
  }, [condition, data, hasShown]);

  if (!shouldShow) {
    return fallback ? <>{fallback}</> : null;
  }

  return (
    <div 
      className={cn(
        animateEntry && hasShown && "animate-in slide-in-from-top-2 duration-300",
        className
      )}
    >
      {children}
    </div>
  );
};

// Field group component for organizing related fields
export const FieldGroup: React.FC<FieldGroupProps> = ({
  title,
  description,
  required,
  children,
  className,
  variant = 'default',
  collapsible = false,
  defaultOpen = true,
  icon: Icon
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const content = (
    <div className={cn(
      "space-y-4",
      variant === 'card' && "p-4",
      variant === 'bordered' && "p-4 border rounded-lg",
      className
    )}>
      {children}
    </div>
  );

  if (!title && !description) {
    return content;
  }

  if (collapsible) {
    return (
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className={className}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-0 h-auto hover:bg-transparent"
          >
            <div className="flex items-center space-x-2 text-left">
              {Icon && <Icon className="h-4 w-4 text-gray-600" />}
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">{title}</span>
                  {required && <Badge variant="secondary" className="text-xs">Required</Badge>}
                </div>
                {description && (
                  <p className="text-sm text-gray-600 mt-1">{description}</p>
                )}
              </div>
            </div>
            {isOpen ? (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-4">
          {content}
        </CollapsibleContent>
      </Collapsible>
    );
  }

  return (
    <div className={className}>
      <div className="mb-4">
        <div className="flex items-center space-x-2">
          {Icon && <Icon className="h-4 w-4 text-gray-600" />}
          <h3 className="font-medium text-gray-900">{title}</h3>
          {required && <Badge variant="secondary" className="text-xs">Required</Badge>}
        </div>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>
      {content}
    </div>
  );
};

// Main form disclosure component
export const FormDisclosure: React.FC<FormDisclosureProps> = ({
  sections,
  data = {},
  className,
  allowMultipleOpen = true,
  showProgress = false,
  mobileOptimized = true,
  onSectionToggle
}) => {
  const [openSections, setOpenSections] = useState<Set<string>>(
    new Set(sections.filter(s => s.defaultOpen !== false).map(s => s.id))
  );

  // Filter visible sections based on conditions
  const visibleSections = sections.filter(section => 
    !section.condition || section.condition(data)
  );

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => {
      const newSet = new Set(prev);
      const isCurrentlyOpen = newSet.has(sectionId);
      
      if (!allowMultipleOpen && !isCurrentlyOpen) {
        // Close all other sections
        newSet.clear();
      }
      
      if (isCurrentlyOpen) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      
      onSectionToggle?.(sectionId, !isCurrentlyOpen);
      return newSet;
    });
  };

  // Calculate progress if enabled
  const progress = showProgress ? {
    total: visibleSections.length,
    completed: visibleSections.filter(s => openSections.has(s.id)).length
  } : null;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Progress indicator */}
      {progress && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">
                  Form Progress
                </span>
              </div>
              <Badge variant="outline" className="text-blue-700 border-blue-300">
                {progress.completed} of {progress.total} sections
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sections */}
      {visibleSections.map((section) => {
        const isOpen = openSections.has(section.id);
        const Icon = section.icon;
        
        const sectionContent = (
          <div key={section.id} className="space-y-2">
            {section.variant === 'minimal' ? (
              // Minimal variant - no card wrapper
              <div>
                {(section.title || section.description) && (
                  <div 
                    className={cn(
                      "flex items-center justify-between py-2",
                      section.collapsible !== false && "cursor-pointer hover:bg-gray-50 rounded px-2 -mx-2"
                    )}
                    onClick={section.collapsible !== false ? () => toggleSection(section.id) : undefined}
                  >
                    <div className="flex items-center space-x-2">
                      {Icon && <Icon className="h-4 w-4 text-gray-600" />}
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{section.title}</span>
                          {section.required && (
                            <Badge variant="secondary" className="text-xs">Required</Badge>
                          )}
                          {section.badge && (
                            <Badge variant="outline" className="text-xs">{section.badge}</Badge>
                          )}
                        </div>
                        {section.description && (
                          <p className="text-sm text-gray-600">{section.description}</p>
                        )}
                      </div>
                    </div>
                    {section.collapsible !== false && (
                      <div className="flex items-center space-x-1">
                        {isOpen ? (
                          <Minus className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Plus className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                    )}
                  </div>
                )}
                {(isOpen || section.collapsible === false) && (
                  <div className="pl-6">
                    {section.children}
                  </div>
                )}
              </div>
            ) : (
              // Card variants
              <Card className={cn(
                section.variant === 'outlined' && "border-2",
                isOpen && section.required && "border-l-4 border-l-orange-500",
                isOpen && !section.required && "border-l-4 border-l-blue-500"
              )}>
                {(section.title || section.description) && (
                  <CardHeader 
                    className={cn(
                      "pb-3",
                      section.collapsible !== false && "cursor-pointer hover:bg-gray-50"
                    )}
                    onClick={section.collapsible !== false ? () => toggleSection(section.id) : undefined}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {Icon && <Icon className="h-5 w-5 text-gray-600" />}
                        <div>
                          <CardTitle className="text-base flex items-center space-x-2">
                            <span>{section.title}</span>
                            {section.required && (
                              <Badge variant="secondary" className="text-xs">Required</Badge>
                            )}
                            {section.badge && (
                              <Badge variant="outline" className="text-xs">{section.badge}</Badge>
                            )}
                          </CardTitle>
                          {section.description && (
                            <p className="text-sm text-gray-600 mt-1">{section.description}</p>
                          )}
                        </div>
                      </div>
                      {section.collapsible !== false && (
                        <div className="flex items-center space-x-1">
                          {isOpen ? (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-400" />
                          )}
                          {isOpen ? (
                            <ChevronDown className="h-4 w-4 text-gray-400" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                          )}
                        </div>
                      )}
                    </div>
                  </CardHeader>
                )}
                
                {(isOpen || section.collapsible === false) && (
                  <CardContent className={cn(
                    section.title || section.description ? "pt-0" : "pt-6"
                  )}>
                    {section.children}
                  </CardContent>
                )}
              </Card>
            )}
          </div>
        );

        return sectionContent;
      })}

      {/* Mobile controls */}
      {mobileOptimized && visibleSections.length > 1 && (
        <div className="block sm:hidden">
          <Card className="bg-gray-50">
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Quick Actions</span>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allIds = new Set(visibleSections.map(s => s.id));
                      setOpenSections(allIds);
                    }}
                    className="text-xs"
                  >
                    Expand All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setOpenSections(new Set())}
                    className="text-xs"
                  >
                    Collapse All
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
