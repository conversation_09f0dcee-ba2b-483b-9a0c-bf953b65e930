
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';
import { TaskFormData } from './types';
import { NotificationService } from '@/services/notificationService';
import { useAuth } from '@/hooks/useAuth';

// Hook for creating a new task
export const useCreateTask = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user, profile } = useAuth();

  return useMutation({
    mutationFn: async (taskData: TaskFormData) => {
      console.log('🔨 Creating task with data:', taskData);
      
      const { data, error } = await supabase
        .rpc('create_task', {
          p_title: taskData.title,
          p_description: taskData.description || null,
          p_priority: taskData.priority,
          p_due_date: taskData.due_date ? taskData.due_date.toISOString() : null,
          p_assigned_to: taskData.assigned_to || null,
          p_school_id: taskData.school_id || null
        });

      if (error) {
        console.error('❌ Error creating task:', error);
        throw error;
      }
      
      console.log('✅ Task created successfully:', data);
      return data;
    },
    onSuccess: async (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['my-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['managed-tasks'] });
      console.log('✅ Task queries invalidated after creation');

      // Send notification if task is assigned to someone other than the creator
      if (variables.assigned_to && variables.assigned_to !== user?.id && user && profile) {
        try {
          await NotificationService.notifyTaskAssigned(
            data.id,
            variables.title,
            variables.assigned_to,
            profile.name || 'Unknown',
            user.id,
            profile.role
          );
          console.log('✅ Task assignment notification sent');
        } catch (error) {
          console.error('❌ Failed to send task assignment notification:', error);
        }
      }

      toast({
        title: "Success",
        description: "Task created successfully",
      });
    },
    onError: (error: Error) => {
      console.error('❌ Task creation failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create task",
        variant: "destructive",
      });
    },
  });
};

// Hook for updating task status
export const useUpdateTaskStatus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user, profile } = useAuth();

  return useMutation({
    mutationFn: async ({ taskId, status }: { taskId: string; status: Database['public']['Enums']['task_status'] }) => {
      console.log('🔄 Updating task status:', taskId, 'to', status);
      
      const { data, error } = await supabase
        .from('tasks')
        .update({ status })
        .eq('id', taskId)
        .select();

      if (error) {
        console.error('❌ Error updating task status:', error);
        throw error;
      }
      
      console.log('✅ Task status updated successfully:', data);
      return data;
    },
    onSuccess: async (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['my-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['managed-tasks'] });
      console.log('✅ Task queries invalidated after status update');

      // Send notification if task is completed
      if (variables.status === 'completed' && data && data.length > 0 && user && profile) {
        const task = data[0];
        try {
          // Get task creator info to send notification
          const { data: creatorData } = await supabase
            .from('profiles')
            .select('id, name, role')
            .eq('id', task.created_by)
            .single();

          if (creatorData && creatorData.id !== user.id) {
            await NotificationService.createNotification(
              'task_completed',
              'Task Completed',
              `Task "${task.title}" has been marked as completed by ${profile.name || 'Unknown'}`,
              [{ id: creatorData.id, role: creatorData.role, name: creatorData.name }],
              {
                priority: 'low',
                senderId: user.id,
                senderRole: profile.role,
                context: {
                  entityType: 'task',
                  entityId: task.id,
                  actionUrl: 'tasks',
                  metadata: { task_title: task.title, completed_by: profile.name }
                }
              }
            );
            console.log('✅ Task completion notification sent');
          }
        } catch (error) {
          console.error('❌ Failed to send task completion notification:', error);
        }
      }

      toast({
        title: "Success",
        description: "Task status updated successfully",
      });
    },
    onError: (error: Error) => {
      console.error('❌ Task status update failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update task status",
        variant: "destructive",
      });
    },
  });
};
