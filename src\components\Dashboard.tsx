import React from 'react';
import { Home } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { ComprehensiveAdminDashboard } from './dashboard/ComprehensiveAdminDashboard';
import { FieldStaffDashboard } from './dashboard/FieldStaffDashboard';
import { ProgramOfficerDashboard } from './dashboard/ProgramOfficerDashboard';

interface DashboardProps {
  onViewChange?: (view: string) => void;
}

const Dashboard = ({ onViewChange }: DashboardProps) => {
  const { profile, loading } = useAuth();

  // Show loading state while profile is being fetched
  if (loading || !profile) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-ilead-green mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Dashboard</h3>
            <p className="text-gray-600">Please wait while we prepare your dashboard...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Route to role-specific dashboards
  if (profile.role === 'admin') {
    return <ComprehensiveAdminDashboard onViewChange={onViewChange} />;
  }

  if (profile.role === 'program_officer') {
    return <ProgramOfficerDashboard onViewChange={onViewChange} />;
  }

  if (profile.role === 'field_staff') {
    return <FieldStaffDashboard onViewChange={onViewChange} />;
  }

  // Fallback for unknown roles
  return (
    <PageLayout>
      <PageHeader
        title="Dashboard Access Error"
        description="Your user role is not recognized"
        icon={Home}
      />
      <ContentCard>
        <div className="text-center py-12">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <Home className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Dashboard Access Error
          </h3>
          <p className="text-gray-600 mb-4">
            Your user role "{profile.role}" is not recognized by the system.
          </p>
          <p className="text-sm text-gray-500">
            Please contact your administrator to resolve this issue.
          </p>
        </div>
      </ContentCard>
    </PageLayout>
  );
};

export default Dashboard;
