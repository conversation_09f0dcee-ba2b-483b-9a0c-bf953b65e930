import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Lock } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';

interface AdminOnlyWrapperProps {
  children: React.ReactNode;
  fallbackMessage?: string;
}

const AdminOnlyWrapper: React.FC<AdminOnlyWrapperProps> = ({
  children,
  fallbackMessage = "Access to Impact Measurement is restricted to administrators only."
}) => {
  const { profile, loading } = useAuth();
  const { roleChecker } = useAccessControl();

  // Show loading state while authentication is being resolved
  if (loading) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Verifying access permissions...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isAdmin = roleChecker.isAdmin();

  if (!isAdmin) {
    return (
      <div className="p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-8 text-center">
            <div className="bg-red-100 p-4 rounded-lg inline-block mb-6">
              <Shield className="h-12 w-12 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-red-800 mb-4">
              Administrator Access Required
            </h2>
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Lock className="h-5 w-5 text-red-600" />
              <p className="text-red-700 font-medium">
                Restricted Access
              </p>
            </div>
            <p className="text-red-600 mb-6 max-w-md mx-auto">
              {fallbackMessage}
            </p>
            <div className="bg-red-100 p-4 rounded-lg">
              <p className="text-sm text-red-700">
                <strong>Current Role:</strong> {profile?.role || 'Unknown'}
              </p>
              <p className="text-sm text-red-700 mt-1">
                <strong>Required Role:</strong> Administrator
              </p>
            </div>
            <p className="text-sm text-red-500 mt-4">
              Please contact your system administrator if you believe you should have access to this feature.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};

export default AdminOnlyWrapper;
