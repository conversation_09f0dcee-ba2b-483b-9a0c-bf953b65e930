-- Create server-side role validation and security functions
-- This migration creates functions to verify role permissions on the backend

-- Function to validate user role and permissions
CREATE OR REPLACE FUNCTION validate_user_role_access(
    p_required_roles user_role[],
    p_target_user_id UUID DEFAULT NULL,
    p_allow_self_access BOOLEAN DEFAULT false,
    p_require_ownership BOOLEAN DEFAULT false
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role user_role;
    validation_result JSON;
    is_owner BOOLEAN := false;
    has_elevated_access BOOLEAN := false;
BEGIN
    -- Get current user info
    current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF current_user_id IS NULL THEN
        RETURN jsonb_build_object(
            'has_access', false,
            'reason', 'User not authenticated',
            'error_code', 'AUTH_REQUIRED'
        );
    END IF;
    
    -- Get user role
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    IF current_user_role IS NULL THEN
        RETURN jsonb_build_object(
            'has_access', false,
            'reason', 'User profile not found',
            'error_code', 'PROFILE_NOT_FOUND'
        );
    END IF;
    
    -- Check if user has required role
    IF p_required_roles IS NOT NULL AND array_length(p_required_roles, 1) > 0 THEN
        IF NOT (current_user_role = ANY(p_required_roles)) THEN
            RETURN jsonb_build_object(
                'has_access', false,
                'reason', format('Role %s not in required roles: %s', current_user_role, array_to_string(p_required_roles, ', ')),
                'error_code', 'INSUFFICIENT_ROLE',
                'current_role', current_user_role,
                'required_roles', p_required_roles
            );
        END IF;
    END IF;
    
    -- Check ownership if required
    IF p_target_user_id IS NOT NULL THEN
        is_owner := (current_user_id = p_target_user_id);
        
        IF p_require_ownership AND NOT is_owner THEN
            RETURN jsonb_build_object(
                'has_access', false,
                'reason', 'User does not own this resource',
                'error_code', 'OWNERSHIP_REQUIRED',
                'is_owner', false
            );
        END IF;
    END IF;
    
    -- Check elevated access
    has_elevated_access := (current_user_role IN ('admin', 'program_officer'));
    
    -- Field staff can only access their own data unless they have elevated access
    IF current_user_role = 'field_staff' AND p_target_user_id IS NOT NULL AND NOT is_owner AND NOT has_elevated_access THEN
        RETURN jsonb_build_object(
            'has_access', false,
            'reason', 'Field staff can only access their own data',
            'error_code', 'FIELD_STAFF_RESTRICTION',
            'is_owner', false
        );
    END IF;
    
    -- Access granted
    RETURN jsonb_build_object(
        'has_access', true,
        'reason', 'Access granted',
        'current_user_id', current_user_id,
        'current_role', current_user_role,
        'is_owner', is_owner,
        'has_elevated_access', has_elevated_access
    );
END;
$$;

-- Function to validate API endpoint access
CREATE OR REPLACE FUNCTION validate_api_endpoint_access(
    p_endpoint_name TEXT,
    p_http_method TEXT DEFAULT 'GET',
    p_target_user_id UUID DEFAULT NULL,
    p_resource_id UUID DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role user_role;
    validation_result JSON;
    required_roles user_role[];
    allow_self_access BOOLEAN := false;
    require_ownership BOOLEAN := false;
BEGIN
    current_user_id := auth.uid();
    
    -- Get user role
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    -- Define endpoint access rules
    CASE p_endpoint_name
        -- Dashboard endpoints
        WHEN 'dashboard_metrics' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            
        -- Field reports endpoints
        WHEN 'field_reports' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            allow_self_access := true;
            
        WHEN 'field_reports_create' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            require_ownership := (current_user_role = 'field_staff');
            
        WHEN 'field_reports_update' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            require_ownership := (current_user_role = 'field_staff');
            
        WHEN 'field_reports_delete' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        -- Field staff attendance endpoints
        WHEN 'field_staff_attendance' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            allow_self_access := true;
            
        WHEN 'field_staff_attendance_create' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            require_ownership := (current_user_role = 'field_staff');
            
        -- Tasks endpoints
        WHEN 'tasks' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            allow_self_access := true;
            
        WHEN 'tasks_create' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'tasks_update' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'tasks_delete' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        -- Schools endpoints
        WHEN 'schools' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            
        WHEN 'schools_create' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'schools_update' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'schools_delete' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        -- Books endpoints
        WHEN 'books' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'books_create' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'books_update' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'books_delete' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        -- Staff management endpoints
        WHEN 'staff_management' THEN
            required_roles := ARRAY['admin', 'program_officer']::user_role[];
            
        WHEN 'staff_create' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        WHEN 'staff_update' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        WHEN 'staff_delete' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        -- Impact measurement endpoints
        WHEN 'impact' THEN
            required_roles := ARRAY['admin']::user_role[];
            
        -- Profile endpoints
        WHEN 'profile' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            allow_self_access := true;
            require_ownership := (current_user_role = 'field_staff');
            
        WHEN 'profile_update' THEN
            required_roles := ARRAY['admin', 'program_officer', 'field_staff']::user_role[];
            require_ownership := (current_user_role = 'field_staff');
            
        ELSE
            RETURN jsonb_build_object(
                'has_access', false,
                'reason', format('Unknown endpoint: %s', p_endpoint_name),
                'error_code', 'UNKNOWN_ENDPOINT'
            );
    END CASE;
    
    -- Validate access using the role validation function
    SELECT validate_user_role_access(
        required_roles,
        p_target_user_id,
        allow_self_access,
        require_ownership
    ) INTO validation_result;
    
    -- Add endpoint-specific information
    validation_result := validation_result || jsonb_build_object(
        'endpoint_name', p_endpoint_name,
        'http_method', p_http_method,
        'required_roles', required_roles,
        'allow_self_access', allow_self_access,
        'require_ownership', require_ownership
    );
    
    RETURN validation_result;
END;
$$;

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type TEXT,
    p_endpoint_name TEXT DEFAULT NULL,
    p_target_user_id UUID DEFAULT NULL,
    p_details JSONB DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_role user_role;
BEGIN
    current_user_id := auth.uid();
    
    -- Get user role
    SELECT role INTO current_user_role
    FROM profiles
    WHERE id = current_user_id;
    
    -- Insert security log entry
    INSERT INTO security_logs (
        user_id,
        user_role,
        event_type,
        endpoint_name,
        target_user_id,
        details,
        created_at
    ) VALUES (
        current_user_id,
        current_user_role,
        p_event_type,
        p_endpoint_name,
        p_target_user_id,
        p_details,
        NOW()
    );
END;
$$;

-- Create security logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS security_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES profiles(id),
    user_role user_role,
    event_type TEXT NOT NULL,
    endpoint_name TEXT,
    target_user_id UUID,
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for security logs
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at);

-- Enable RLS on security logs
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;

-- RLS policy for security logs (admin only)
CREATE POLICY "Admin can view all security logs" ON security_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION validate_user_role_access(user_role[], UUID, BOOLEAN, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_api_endpoint_access(TEXT, TEXT, UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION log_security_event(TEXT, TEXT, UUID, JSONB) TO authenticated;
