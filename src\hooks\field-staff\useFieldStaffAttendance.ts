import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { useOfflineSync } from './useOfflineSync';

type FieldStaffAttendance = Database['public']['Tables']['field_staff_attendance']['Row'];
type FieldStaffAttendanceInsert = Database['public']['Tables']['field_staff_attendance']['Insert'];
type FieldReport = Database['public']['Tables']['field_reports']['Row'];
type FieldActivityType = Database['public']['Enums']['field_activity_type'];

interface CheckInData {
  school_id: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  address?: string;
  verification_method?: string;
  device_info?: Record<string, unknown>;
  network_info?: Record<string, unknown>;
  offline_sync?: boolean;
}

interface CheckOutData {
  attendance_id: string;
  latitude?: number; // Optional - check-out location no longer required
  longitude?: number; // Optional - check-out location no longer required
  accuracy?: number;
  address?: string;
  notes?: string;
  activity_type: FieldActivityType;
  round_table_sessions?: number;
  total_students?: number;
  students_per_session?: number;
  activities_conducted?: string[];
  topics_covered?: string[];
  challenges?: string;
  wins?: string;
  observations?: string;
  lessons_learned?: string;
  follow_up_required?: boolean;
  follow_up_actions?: string;
  photos?: string[];
  offline_sync?: boolean;
}

interface TimesheetData {
  timesheet_id: string;
  staff_id: string;
  staff_name: string;
  staff_role: string;
  timesheet_date: string;
  total_work_hours: number;
  total_schools_visited: number;
  total_students_reached: number;
  total_sessions_conducted: number;
  schools_visited: Record<string, unknown>;
  key_achievements: string;
  main_challenges: string;
  timesheet_status: string;
  productivity_score: number;
  submitted_at: string;
}

interface AttendanceStatusData {
  staff_id: string;
  staff_name: string;
  attendance_date: string;
  total_check_ins: number;
  current_status: string;
  current_school_name: string;
  check_in_time: string;
  hours_worked: number;
  schools_visited_today: number;
}

// Hook for field staff check-in
export const useFieldStaffCheckIn = () => {
  const queryClient = useQueryClient();
  const { addToOfflineQueue } = useOfflineSync();

  return useMutation({
    mutationFn: async (data: CheckInData) => {
      // If offline, queue the data for later sync with high priority
      if (!navigator.onLine || data.offline_sync) {
        const offlineId = addToOfflineQueue('check_in', data, 'HIGH');
        return offlineId;
      }

      const { data: result, error } = await supabase.rpc('field_staff_checkin', {
        p_school_id: data.school_id,
        p_latitude: data.latitude,
        p_longitude: data.longitude,
        p_accuracy: data.accuracy,
        p_address: data.address,
        p_verification_method: data.verification_method || 'gps',
        p_device_info: data.device_info || {},
        p_network_info: data.network_info || {},
        p_offline_sync: data.offline_sync || false,
      });

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      if (navigator.onLine) {
        toast.success('Successfully checked in!');
      } else {
        toast.success('Check-in saved offline. Will sync when connection is restored.');
      }
      queryClient.invalidateQueries({ queryKey: ['field-staff-attendance'] });
      queryClient.invalidateQueries({ queryKey: ['field-staff-status'] });
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to check in');
    },
  });
};

// Hook for field staff check-out
export const useFieldStaffCheckOut = () => {
  const queryClient = useQueryClient();
  const { addToOfflineQueue } = useOfflineSync();

  return useMutation({
    mutationFn: async (data: CheckOutData) => {
      // If offline, queue the data for later sync with critical priority
      if (!navigator.onLine || data.offline_sync) {
        const offlineId = addToOfflineQueue('check_out', data, 'CRITICAL');
        return offlineId;
      }

      // Validate that the attendance record exists before attempting checkout
      const { data: attendanceCheck, error: checkError } = await supabase
        .from('field_staff_attendance')
        .select('id, status, check_out_time')
        .eq('id', data.attendance_id)
        .maybeSingle();

      if (checkError) {
        throw new Error(`Failed to verify attendance record: ${checkError.message}`);
      }

      if (!attendanceCheck) {
        throw new Error(`Attendance record not found. Please refresh the page and try again.`);
      }

      if (attendanceCheck.check_out_time) {
        throw new Error(`You have already checked out. Please refresh the page.`);
      }

      const { data: result, error } = await supabase.rpc('field_staff_checkout', {
        p_attendance_id: data.attendance_id,
        p_activity_type: data.activity_type,
        p_latitude: data.latitude,
        p_longitude: data.longitude,
        p_accuracy: data.accuracy,
        p_address: data.address,
        p_notes: data.notes,
        p_round_table_sessions: data.round_table_sessions || 0,
        p_total_students: data.total_students || 0,
        p_students_per_session: data.students_per_session || 8,
        p_activities_conducted: data.activities_conducted || [],
        p_topics_covered: data.topics_covered || [],
        p_challenges: data.challenges,
        p_wins: data.wins,
        p_observations: data.observations,
        p_lessons_learned: data.lessons_learned,
        p_follow_up_required: data.follow_up_required || false,
        p_follow_up_actions: data.follow_up_actions,
        p_photos: data.photos || [],
        p_offline_sync: data.offline_sync || false,
      });

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      if (navigator.onLine) {
        toast.success('Successfully checked out and submitted field report!');
      } else {
        toast.success('Check-out and report saved offline. Will sync when connection is restored.');
      }
      // Clear all related cache to ensure fresh data
      queryClient.invalidateQueries({ queryKey: ['field-staff-attendance'] });
      queryClient.invalidateQueries({ queryKey: ['field-staff-status'] });
      queryClient.invalidateQueries({ queryKey: ['field-staff-timesheets'] });
      queryClient.invalidateQueries({ queryKey: ['current-attendance'] });
      queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      queryClient.invalidateQueries({ queryKey: ['unified-check-in-status'] });
    },
    onError: (error: Error) => {
      // Clear cache on error to force fresh data fetch
      queryClient.invalidateQueries({ queryKey: ['current-attendance'] });
      queryClient.invalidateQueries({ queryKey: ['unified-check-in-status'] });
      toast.error(error.message || 'Failed to check out');
    },
  });
};

// Helper function to refresh attendance data (useful for debugging sync issues)
export const useRefreshAttendanceData = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({ queryKey: ['current-attendance'] });
    queryClient.invalidateQueries({ queryKey: ['unified-check-in-status'] });
    queryClient.invalidateQueries({ queryKey: ['field-staff-attendance'] });
    queryClient.invalidateQueries({ queryKey: ['field-staff-status'] });
    toast.info('Refreshing attendance data...');
  };
};

// Helper function to check if user is actually checked in (for debugging sync issues)
export const useVerifyCheckInStatus = () => {
  return useQuery({
    queryKey: ['verify-check-in-status'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('field_staff_attendance')
        .select('id, check_in_time, check_out_time, status, school_id')
        .eq('attendance_date', new Date().toISOString().split('T')[0])
        .is('check_out_time', null)
        .order('check_in_time', { ascending: false })
        .maybeSingle();

      if (error) throw error;

      return {
        isActuallyCheckedIn: !!data,
        attendanceRecord: data,
        message: data
          ? `You are checked in at ${data.check_in_time}`
          : 'You are not currently checked in according to the database'
      };
    },
    enabled: false, // Only run when manually triggered
  });
};

// Hook to get current attendance status
export const useFieldStaffAttendanceStatus = (date?: string, staffId?: string) => {
  return useQuery({
    queryKey: ['field-staff-status', date, staffId],
    queryFn: async (): Promise<AttendanceStatusData[]> => {
      const { data, error } = await supabase.rpc('get_field_staff_attendance_status', {
        p_date: date || new Date().toISOString().split('T')[0],
        p_staff_id: staffId,
      });

      if (error) throw error;
      return data || [];
    },
  });
};

// Hook to get field staff timesheets
export const useFieldStaffTimesheets = (date?: string, staffId?: string) => {
  return useQuery({
    queryKey: ['field-staff-timesheets', date, staffId],
    queryFn: async (): Promise<TimesheetData[]> => {
      const { data, error } = await supabase.rpc('get_field_staff_timesheets', {
        p_date: date || new Date().toISOString().split('T')[0],
        p_staff_id: staffId,
      });

      if (error) throw error;
      return data || [];
    },
  });
};

// Hook to get current user's active attendance
export const useCurrentAttendance = () => {
  const { user, profile } = useAuth();

  return useQuery({
    queryKey: ['current-attendance', user?.id, profile?.role],
    queryFn: async (): Promise<FieldStaffAttendance | null> => {
      if (!user?.id) {
        return null;
      }

      // Use direct table query instead of RPC function to avoid the 406 error
      // First try to get active attendance for today
      const { data: activeData, error: activeError } = await supabase
        .from('field_staff_attendance')
        .select('*')
        .eq('staff_id', user.id)
        .eq('attendance_date', new Date().toISOString().split('T')[0])
        .eq('status', 'active')
        .maybeSingle();

      if (activeError) throw activeError;

      // If we found an active record, return it
      if (activeData) {
        return activeData;
      }

      // If no active record found, check for any attendance record today that hasn't been checked out
      const { data: uncheckedData, error: uncheckedError } = await supabase
        .from('field_staff_attendance')
        .select('*')
        .eq('staff_id', user.id)
        .eq('attendance_date', new Date().toISOString().split('T')[0])
        .is('check_out_time', null)
        .order('check_in_time', { ascending: false })
        .maybeSingle();

      if (uncheckedError) throw uncheckedError;
      return uncheckedData;
    },
    // Refetch more frequently to catch state changes
    refetchInterval: 30000, // 30 seconds
    staleTime: 10000, // 10 seconds
  });
};

// Hook to get field reports
export const useFieldReports = (staffId?: string, startDate?: string, endDate?: string) => {
  return useQuery({
    queryKey: ['field-reports', staffId, startDate, endDate],
    queryFn: async (): Promise<FieldReport[]> => {
      let query = supabase
        .from('field_reports')
        .select('*, field_staff_attendance!inner(school_id), schools!field_staff_attendance(name)')
        .order('report_date', { ascending: false });

      if (staffId) {
        query = query.eq('staff_id', staffId);
      }

      if (startDate) {
        query = query.gte('report_date', startDate);
      }

      if (endDate) {
        query = query.lte('report_date', endDate);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    },
  });
};
