import { z } from 'zod';

/**
 * Common validation patterns
 */
export const commonPatterns = {
  email: z.string().email('Invalid email address'),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format'),
  uuid: z.string().uuid('Invalid UUID format'),
  positiveInteger: z.number().int().positive('Must be a positive integer'),
  nonNegativeInteger: z.number().int().min(0, 'Must be zero or positive'),
  year: z.number().int().min(1900).max(new Date().getFullYear() + 10, 'Invalid year'),
  percentage: z.number().min(0).max(100, 'Must be between 0 and 100'),
  url: z.string().url('Invalid URL format'),
  isbn: z.string().regex(/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/, 'Invalid ISBN format'),
};

/**
 * Text field validation with length limits
 */
export const createTextSchema = (
  minLength = 0,
  maxLength = 255,
  required = true,
  customMessage?: string
) => {
  let schema = z.string();
  
  if (required) {
    schema = schema.min(1, customMessage || 'This field is required');
  }
  
  if (minLength > 0) {
    schema = schema.min(minLength, `Must be at least ${minLength} characters`);
  }
  
  if (maxLength > 0) {
    schema = schema.max(maxLength, `Must be no more than ${maxLength} characters`);
  }
  
  return schema;
};

/**
 * User management schemas
 */
export const userSchemas = {
  profile: z.object({
    first_name: createTextSchema(1, 100),
    last_name: createTextSchema(1, 100),
    email: commonPatterns.email,
    phone: commonPatterns.phone.optional(),
    role: z.enum(['admin', 'program_officer', 'field_staff']),
  }),
  
  createUser: z.object({
    first_name: createTextSchema(1, 100),
    last_name: createTextSchema(1, 100),
    email: commonPatterns.email,
    role: z.enum(['admin', 'program_officer', 'field_staff']),
    password: createTextSchema(8, 128, true, 'Password must be at least 8 characters'),
  }),
  
  updateUser: z.object({
    first_name: createTextSchema(1, 100).optional(),
    last_name: createTextSchema(1, 100).optional(),
    email: commonPatterns.email.optional(),
    phone: commonPatterns.phone.optional(),
    role: z.enum(['admin', 'program_officer', 'field_staff']).optional(),
  }),
};

/**
 * School management schemas
 */
export const schoolSchemas = {
  school: z.object({
    name: createTextSchema(1, 255),
    district: createTextSchema(1, 100),
    school_type: z.enum(['primary', 'secondary', 'tertiary', 'vocational']),
    school_code: createTextSchema(0, 50, false),
    date_joined: z.string().optional(),
    head_teacher_name: createTextSchema(0, 255, false),
    head_teacher_email: commonPatterns.email.optional(),
    deputy_teacher_name: createTextSchema(0, 255, false),
    deputy_teacher_email: commonPatterns.email.optional(),
    champion_teacher_name: createTextSchema(1, 255),
    champion_teacher_email: commonPatterns.email.optional(),
    assistant_champion_teacher_name: createTextSchema(0, 255, false),
    champion_teacher_count: commonPatterns.positiveInteger,
    student_count: commonPatterns.positiveInteger,
    status: z.enum(['active', 'inactive']).default('active'),
  }),
  
  updateSchool: z.object({
    name: createTextSchema(1, 255).optional(),
    district: createTextSchema(1, 100).optional(),
    school_type: z.enum(['primary', 'secondary', 'tertiary', 'vocational']).optional(),
    school_code: createTextSchema(0, 50, false).optional(),
    date_joined: z.string().optional(),
    head_teacher_name: createTextSchema(0, 255, false).optional(),
    head_teacher_email: commonPatterns.email.optional(),
    deputy_teacher_name: createTextSchema(0, 255, false).optional(),
    deputy_teacher_email: commonPatterns.email.optional(),
    champion_teacher_name: createTextSchema(1, 255).optional(),
    champion_teacher_email: commonPatterns.email.optional(),
    assistant_champion_teacher_name: createTextSchema(0, 255, false).optional(),
    champion_teacher_count: commonPatterns.positiveInteger.optional(),
    student_count: commonPatterns.positiveInteger.optional(),
    status: z.enum(['active', 'inactive']).optional(),
  }),
};

/**
 * Book management schemas
 */
export const bookSchemas = {
  book: z.object({
    title: z.enum(['iLead', 'iDo', 'iChoose']),
    language: z.enum(['english', 'luganda', 'runyankole', 'rukiga', 'luo', 'ateso']),
    description: createTextSchema(0, 1000, false),
    total_quantity: commonPatterns.positiveInteger,
    condition: z.enum(['new', 'good', 'fair', 'poor']),
    minimum_threshold: commonPatterns.nonNegativeInteger,
    notes: createTextSchema(0, 500, false),
  }),
  
  updateBook: z.object({
    title: z.enum(['iLead', 'iDo', 'iChoose']).optional(),
    language: z.enum(['english', 'luganda', 'runyankole', 'rukiga', 'luo', 'ateso']).optional(),
    description: createTextSchema(0, 1000, false).optional(),
    total_quantity: commonPatterns.positiveInteger.optional(),
    condition: z.enum(['new', 'good', 'fair', 'poor']).optional(),
    minimum_threshold: commonPatterns.nonNegativeInteger.optional(),
    notes: createTextSchema(0, 500, false).optional(),
  }),
  
  distribution: z.object({
    school_id: commonPatterns.uuid,
    inventory_id: commonPatterns.uuid,
    quantity: commonPatterns.positiveInteger,
    distribution_date: z.string(),
    notes: createTextSchema(0, 500, false),
    status: z.enum(['planned', 'in_progress', 'completed', 'cancelled']).default('planned'),
  }),
};

/**
 * Task management schemas
 */
export const taskSchemas = {
  task: z.object({
    title: createTextSchema(1, 255),
    description: createTextSchema(0, 1000, false),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
    status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).default('pending'),
    due_date: z.union([z.string(), z.date()]).optional(),
    assigned_to: commonPatterns.uuid.optional(),
    school_id: commonPatterns.uuid.optional(),
  }),
  
  updateTask: z.object({
    title: createTextSchema(1, 255).optional(),
    description: createTextSchema(0, 1000, false).optional(),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).optional(),
    due_date: z.string().optional(),
    assigned_to: commonPatterns.uuid.optional(),
    school_id: commonPatterns.uuid.optional(),
  }),
  
  comment: z.object({
    comment: createTextSchema(1, 1000),
  }),
};

/**
 * Field report schemas
 */
export const fieldReportSchemas = {
  report: z.object({
    activity_type: z.enum(['Round table session', 'School visit', 'Meeting', 'Assessment', 'Other']),
    introduction: createTextSchema(0, 500, false),
    round_table_sessions_count: commonPatterns.nonNegativeInteger.optional(),
    total_students_attended: commonPatterns.nonNegativeInteger.optional(),
    male_students_count: commonPatterns.nonNegativeInteger.optional(),
    lesson_category: z.enum(['iChoose', 'iLead', 'iDo']).optional(),
    lesson_topic: createTextSchema(0, 255, false),
    follow_up_actions: createTextSchema(0, 1000, false),
    school_id: commonPatterns.uuid,
  }),
  
  updateReport: z.object({
    activity_type: z.enum(['Round table session', 'School visit', 'Meeting', 'Assessment', 'Other']).optional(),
    introduction: createTextSchema(0, 500, false).optional(),
    round_table_sessions_count: commonPatterns.nonNegativeInteger.optional(),
    total_students_attended: commonPatterns.nonNegativeInteger.optional(),
    male_students_count: commonPatterns.nonNegativeInteger.optional(),
    lesson_category: z.enum(['iChoose', 'iLead', 'iDo']).optional(),
    lesson_topic: createTextSchema(0, 255, false).optional(),
    follow_up_actions: createTextSchema(0, 1000, false).optional(),
  }),
};

/**
 * Export all schemas
 */
export const validationSchemas = {
  user: userSchemas,
  school: schoolSchemas,
  book: bookSchemas,
  task: taskSchemas,
  fieldReport: fieldReportSchemas,
  common: commonPatterns,
};
