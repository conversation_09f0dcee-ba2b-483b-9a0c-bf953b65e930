import { useCallback, useMemo } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useAccessControl } from '@/hooks/useAccessControl';
import { 
  ServerValidationService, 
  useServerValidation,
  type EndpointValidationResult 
} from '@/utils/serverValidation';
import { toast } from 'sonner';

/**
 * Security middleware hook for protecting operations and API calls
 */
export function useSecurityMiddleware() {
  const { user, profile } = useAuth();
  const { currentUser } = useAccessControl();
  const serverValidation = useServerValidation();

  /**
   * Validate and execute a protected operation
   */
  const executeWithValidation = useCallback(async <T>(
    operation: () => Promise<T>,
    validationConfig: {
      endpointName: string;
      httpMethod?: string;
      targetUserId?: string;
      resourceId?: string;
      showErrorToast?: boolean;
      customErrorMessage?: string;
    }
  ): Promise<{ success: boolean; data?: T; error?: string; validation?: EndpointValidationResult }> => {
    const {
      endpointName,
      httpMethod = 'GET',
      targetUserId,
      resourceId,
      showErrorToast = true,
      customErrorMessage
    } = validationConfig;

    try {
      // Validate access first
      const validation = await serverValidation.validateApiEndpointAccess(
        endpointName,
        httpMethod,
        targetUserId,
        resourceId
      );

      if (!validation.hasAccess) {
        const errorMessage = customErrorMessage || validation.reason || 'Access denied';
        
        if (showErrorToast) {
          toast.error('Access Denied', {
            description: errorMessage,
          });
        }

        // Log the security event
        await serverValidation.logSecurityEvent(
          'ACCESS_DENIED',
          endpointName,
          targetUserId,
          { 
            httpMethod, 
            resourceId, 
            reason: validation.reason,
            errorCode: validation.errorCode 
          }
        );

        return {
          success: false,
          error: errorMessage,
          validation,
        };
      }

      // Execute the operation if validation passes
      const data = await operation();

      // Log successful access
      await serverValidation.logSecurityEvent(
        'ACCESS_GRANTED',
        endpointName,
        targetUserId,
        { httpMethod, resourceId }
      );

      return {
        success: true,
        data,
        validation,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Operation failed';
      
      if (showErrorToast) {
        toast.error('Operation Failed', {
          description: errorMessage,
        });
      }

      // Log the error
      await serverValidation.logSecurityEvent(
        'OPERATION_ERROR',
        endpointName,
        targetUserId,
        { 
          httpMethod, 
          resourceId, 
          error: errorMessage 
        }
      );

      return {
        success: false,
        error: errorMessage,
      };
    }
  }, [serverValidation]);

  /**
   * Validate field reports operations
   */
  const validateFieldReportsOperation = useCallback(async (
    action: 'read' | 'create' | 'update' | 'delete',
    targetUserId?: string,
    reportId?: string
  ) => {
    return await serverValidation.validateFieldReportsAccess(action, targetUserId, reportId);
  }, [serverValidation]);

  /**
   * Validate attendance operations
   */
  const validateAttendanceOperation = useCallback(async (
    action: 'read' | 'create' | 'update',
    targetUserId?: string,
    attendanceId?: string
  ) => {
    return await serverValidation.validateAttendanceAccess(action, targetUserId, attendanceId);
  }, [serverValidation]);

  /**
   * Validate tasks operations
   */
  const validateTasksOperation = useCallback(async (
    action: 'read' | 'create' | 'update' | 'delete',
    targetUserId?: string,
    taskId?: string
  ) => {
    return await serverValidation.validateTasksAccess(action, targetUserId, taskId);
  }, [serverValidation]);

  /**
   * Validate staff management operations
   */
  const validateStaffManagementOperation = useCallback(async (
    action: 'read' | 'create' | 'update' | 'delete',
    targetUserId?: string
  ) => {
    return await serverValidation.validateStaffManagementAccess(action, targetUserId);
  }, [serverValidation]);

  /**
   * Check if current user can perform an operation
   */
  const canPerformOperation = useCallback(async (
    endpointName: string,
    httpMethod: string = 'GET',
    targetUserId?: string,
    resourceId?: string
  ): Promise<boolean> => {
    const validation = await serverValidation.validateApiEndpointAccess(
      endpointName,
      httpMethod,
      targetUserId,
      resourceId
    );
    return validation.hasAccess;
  }, [serverValidation]);

  /**
   * Get security context for current user
   */
  const securityContext = useMemo(() => {
    if (!user || !profile || !currentUser) {
      return {
        isAuthenticated: false,
        userId: null,
        userRole: null,
        hasElevatedAccess: false,
        canViewAllData: false,
        canManageUsers: false,
      };
    }

    return {
      isAuthenticated: true,
      userId: user.id,
      userRole: currentUser.role,
      hasElevatedAccess: currentUser.role === 'admin' || currentUser.role === 'program_officer',
      canViewAllData: currentUser.role === 'admin' || currentUser.role === 'program_officer',
      canManageUsers: currentUser.role === 'admin' || currentUser.role === 'program_officer',
      isAdmin: currentUser.role === 'admin',
      isProgramOfficer: currentUser.role === 'program_officer',
      isFieldStaff: currentUser.role === 'field_staff',
    };
  }, [user, profile, currentUser]);

  /**
   * Protected operation wrapper for common patterns
   */
  const protectedOperations = useMemo(() => ({
    // Field Reports
    createFieldReport: (operation: () => Promise<any>, targetUserId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'field_reports_create',
        httpMethod: 'POST',
        targetUserId,
        customErrorMessage: 'You do not have permission to create field reports',
      }),

    updateFieldReport: (operation: () => Promise<any>, targetUserId?: string, reportId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'field_reports_update',
        httpMethod: 'PUT',
        targetUserId,
        resourceId: reportId,
        customErrorMessage: 'You do not have permission to update this field report',
      }),

    deleteFieldReport: (operation: () => Promise<any>, targetUserId?: string, reportId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'field_reports_delete',
        httpMethod: 'DELETE',
        targetUserId,
        resourceId: reportId,
        customErrorMessage: 'You do not have permission to delete this field report',
      }),

    // Attendance
    createAttendance: (operation: () => Promise<any>, targetUserId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'field_staff_attendance_create',
        httpMethod: 'POST',
        targetUserId,
        customErrorMessage: 'You do not have permission to create attendance records',
      }),

    // Tasks
    createTask: (operation: () => Promise<any>) =>
      executeWithValidation(operation, {
        endpointName: 'tasks_create',
        httpMethod: 'POST',
        customErrorMessage: 'You do not have permission to create tasks',
      }),

    updateTask: (operation: () => Promise<any>, taskId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'tasks_update',
        httpMethod: 'PUT',
        resourceId: taskId,
        customErrorMessage: 'You do not have permission to update this task',
      }),

    deleteTask: (operation: () => Promise<any>, taskId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'tasks_delete',
        httpMethod: 'DELETE',
        resourceId: taskId,
        customErrorMessage: 'You do not have permission to delete this task',
      }),

    // Staff Management
    createStaff: (operation: () => Promise<any>) =>
      executeWithValidation(operation, {
        endpointName: 'staff_create',
        httpMethod: 'POST',
        customErrorMessage: 'You do not have permission to create staff members',
      }),

    updateStaff: (operation: () => Promise<any>, targetUserId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'staff_update',
        httpMethod: 'PUT',
        targetUserId,
        customErrorMessage: 'You do not have permission to update staff information',
      }),

    deleteStaff: (operation: () => Promise<any>, targetUserId?: string) =>
      executeWithValidation(operation, {
        endpointName: 'staff_delete',
        httpMethod: 'DELETE',
        targetUserId,
        customErrorMessage: 'You do not have permission to delete staff members',
      }),
  }), [executeWithValidation]);

  return {
    // Core validation functions
    executeWithValidation,
    canPerformOperation,
    
    // Specific operation validators
    validateFieldReportsOperation,
    validateAttendanceOperation,
    validateTasksOperation,
    validateStaffManagementOperation,
    
    // Security context
    securityContext,
    
    // Protected operation wrappers
    protectedOperations,
    
    // Utility functions
    logSecurityEvent: serverValidation.logSecurityEvent,
    isAuthenticated: securityContext.isAuthenticated,
    currentUserId: securityContext.userId,
    currentUserRole: securityContext.userRole,
  };
}
