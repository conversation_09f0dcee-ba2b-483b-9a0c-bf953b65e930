import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  ResponsiveModal,
  ResponsiveCard,
  ResponsiveGrid,
  ResponsiveButtonGroup,
  ResponsiveForm,
  ResponsiveTabs,
  ResponsiveSpacing,
  useResponsiveBreakpoint
} from '@/components/ui/mobile-responsive';
import { 
  Menu,
  X,
  Home,
  Users,
  Settings,
  Bell,
  Search,
  Plus,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileOptimizedLayoutProps {
  children: React.ReactNode;
  title: string;
  showBottomNav?: boolean;
  showFloatingAction?: boolean;
  onFloatingActionClick?: () => void;
  className?: string;
}

/**
 * Mobile-optimized layout with bottom navigation, floating action button,
 * and responsive patterns
 */
export const MobileOptimizedLayout: React.FC<MobileOptimizedLayoutProps> = ({
  children,
  title,
  showBottomNav = true,
  showFloatingAction = false,
  onFloatingActionClick,
  className
}) => {
  const { isMobile, isTablet } = useResponsiveBreakpoint();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [activeBottomTab, setActiveBottomTab] = useState('home');

  const bottomNavItems = [
    { id: 'home', label: 'Home', icon: Home },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'notifications', label: 'Alerts', icon: Bell },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* Mobile Header */}
      {isMobile && (
        <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between sticky top-0 z-40">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMobileMenu(true)}
              className="btn-mobile-small"
            >
              <Menu className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold text-gray-900 truncate">{title}</h1>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="btn-mobile-small">
              <Search className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="sm" className="btn-mobile-small">
              <Bell className="h-5 w-5" />
            </Button>
          </div>
        </header>
      )}

      {/* Main Content */}
      <main className={cn(
        "flex-1",
        isMobile ? "pb-20" : "pb-4", // Extra padding for bottom nav on mobile
        "container-mobile-full"
      )}>
        <div className={cn(
          isMobile ? "py-4" : "py-6",
          "space-y-4"
        )}>
          {children}
        </div>
      </main>

      {/* Bottom Navigation (Mobile Only) */}
      {isMobile && showBottomNav && (
        <nav className="nav-mobile-bottom">
          <div className="flex">
            {bottomNavItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeBottomTab === item.id;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveBottomTab(item.id)}
                  className={cn(
                    "nav-mobile-tab",
                    isActive ? "text-ilead-green" : "text-gray-400"
                  )}
                >
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs">{item.label}</span>
                </button>
              );
            })}
          </div>
        </nav>
      )}

      {/* Floating Action Button (Mobile Only) */}
      {isMobile && showFloatingAction && onFloatingActionClick && (
        <Button
          onClick={onFloatingActionClick}
          className="fixed bottom-24 right-4 z-50 h-14 w-14 rounded-full bg-ilead-green hover:bg-ilead-dark-green text-white shadow-lg"
        >
          <Plus className="h-6 w-6" />
        </Button>
      )}

      {/* Mobile Menu Modal */}
      <ResponsiveModal
        open={showMobileMenu}
        onOpenChange={setShowMobileMenu}
        title="Menu"
        mobileFullScreen={true}
      >
        <div className="space-y-4">
          <Button variant="outline" className="w-full justify-start btn-mobile">
            <Home className="h-4 w-4 mr-2" />
            Dashboard
          </Button>
          <Button variant="outline" className="w-full justify-start btn-mobile">
            <Users className="h-4 w-4 mr-2" />
            Users
          </Button>
          <Button variant="outline" className="w-full justify-start btn-mobile">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </ResponsiveModal>
    </div>
  );
};

/**
 * Example component demonstrating mobile-first patterns
 */
export const MobileFirstExample: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showModal, setShowModal] = useState(false);
  const { isMobile } = useResponsiveBreakpoint();

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: Home,
      content: (
        <ResponsiveGrid columns={3} mobileStack={true}>
          <ResponsiveCard title="Card 1" mobileCompact={true}>
            <p className="text-mobile-responsive">Mobile-optimized card content</p>
          </ResponsiveCard>
          <ResponsiveCard title="Card 2" mobileCompact={true}>
            <p className="text-mobile-responsive">Responsive design patterns</p>
          </ResponsiveCard>
          <ResponsiveCard title="Card 3" mobileCompact={true}>
            <p className="text-mobile-responsive">Touch-friendly interactions</p>
          </ResponsiveCard>
        </ResponsiveGrid>
      )
    },
    {
      id: 'details',
      label: 'Details',
      icon: Users,
      content: (
        <ResponsiveCard title="Detailed View">
          <ResponsiveForm mobileStack={true}>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <input
                type="text"
                className="input-mobile w-full border border-gray-300 rounded-md"
                placeholder="Enter name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                className="textarea-mobile w-full border border-gray-300 rounded-md"
                placeholder="Enter description"
              />
            </div>
            <ResponsiveButtonGroup mobileStack={true} mobileFullWidth={true}>
              <Button className="bg-ilead-green hover:bg-ilead-dark-green text-white">
                Save
              </Button>
              <Button variant="outline">
                Cancel
              </Button>
            </ResponsiveButtonGroup>
          </ResponsiveForm>
        </ResponsiveCard>
      )
    }
  ];

  return (
    <MobileOptimizedLayout
      title="Mobile-First Demo"
      showBottomNav={isMobile}
      showFloatingAction={isMobile}
      onFloatingActionClick={() => setShowModal(true)}
    >
      {/* Page Header */}
      <ResponsiveCard>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-mobile-heading">Mobile-First Design</h2>
            <p className="text-mobile-responsive text-gray-600">
              Demonstrating responsive patterns and mobile optimization
            </p>
          </div>
          <ResponsiveButtonGroup mobileStack={false}>
            <Button variant="outline" className="btn-mobile">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button className="bg-ilead-green hover:bg-ilead-dark-green text-white btn-mobile">
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </ResponsiveButtonGroup>
        </div>
      </ResponsiveCard>

      <ResponsiveSpacing size="md" />

      {/* Responsive Tabs */}
      <ResponsiveTabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        mobileScrollable={true}
      />

      {/* Modal Example */}
      <ResponsiveModal
        open={showModal}
        onOpenChange={setShowModal}
        title="Add New Item"
        mobileFullScreen={true}
      >
        <ResponsiveForm>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Item Name
            </label>
            <input
              type="text"
              className="input-mobile w-full border border-gray-300 rounded-md"
              placeholder="Enter item name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select className="select-mobile w-full border border-gray-300 rounded-md">
              <option>Select category</option>
              <option>Category 1</option>
              <option>Category 2</option>
            </select>
          </div>
          <ResponsiveSpacing size="lg" />
          <ResponsiveButtonGroup mobileStack={true} mobileFullWidth={true}>
            <Button 
              onClick={() => setShowModal(false)}
              className="bg-ilead-green hover:bg-ilead-dark-green text-white"
            >
              Create Item
            </Button>
            <Button 
              variant="outline"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </Button>
          </ResponsiveButtonGroup>
        </ResponsiveForm>
      </ResponsiveModal>
    </MobileOptimizedLayout>
  );
};
